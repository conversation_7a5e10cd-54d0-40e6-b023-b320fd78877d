/**
 * 直接使用MySQL进行登录验证的示例
 * 这个脚本展示了如何直接使用MySQL进行用户和管理员登录验证
 */
const mysql = require('mysql2');
const jwt = require('jsonwebtoken');
const config = require('./config/auth.config');

// 创建数据库连接
const connection = mysql.createConnection({
  host: '127.0.0.1',
  user: 'root',
  password: '123456',
  database: 'news'
});

// 连接数据库
connection.connect(err => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('数据库连接成功');
});

/**
 * 用户登录函数
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise<Object>} 登录结果
 */
function userLogin(username, password) {
  return new Promise((resolve, reject) => {
    // 查询用户
    const query = 'SELECT * FROM users WHERE username = ?';
    connection.query(query, [username], (err, results) => {
      if (err) {
        console.error('查询用户失败:', err);
        return reject({ message: '服务器错误' });
      }

      // 检查用户是否存在
      if (results.length === 0) {
        return reject({ message: '用户不存在' });
      }

      const user = results[0];
      
      // 验证密码（这里假设密码是明文存储的，实际应用中应该使用加密存储）
      if (user.password !== password) {
        return reject({ message: '密码错误' });
      }

      // 生成JWT令牌
      const token = jwt.sign(
        { id: user.user_id, role: 'user' },
        config.secret,
        { expiresIn: 86400 } // 24小时
      );

      // 返回用户信息和令牌
      resolve({
        id: user.user_id,
        username: user.username,
        role: 'user',
        accessToken: token
      });
    });
  });
}

/**
 * 管理员登录函数
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise<Object>} 登录结果
 */
function adminLogin(username, password) {
  return new Promise((resolve, reject) => {
    // 查询管理员
    const query = 'SELECT * FROM admin_users WHERE username = ?';
    connection.query(query, [username], (err, results) => {
      if (err) {
        console.error('查询管理员失败:', err);
        return reject({ message: '服务器错误' });
      }

      // 检查管理员是否存在
      if (results.length === 0) {
        return reject({ message: '管理员不存在' });
      }

      const admin = results[0];
      
      // 验证密码（这里假设密码是明文存储的，实际应用中应该使用加密存储）
      if (admin.password !== password) {
        return reject({ message: '密码错误' });
      }

      // 生成JWT令牌
      const token = jwt.sign(
        { id: admin.admin_id, role: 'admin' },
        config.secret,
        { expiresIn: 86400 } // 24小时
      );

      // 返回管理员信息和令牌
      resolve({
        id: admin.admin_id,
        username: admin.username,
        role: 'admin',
        accessToken: token
      });
    });
  });
}

// 测试用户登录
async function testUserLogin() {
  try {
    console.log('测试用户登录...');
    const result = await userLogin('testuser', '123456');
    console.log('用户登录成功:', result);
  } catch (error) {
    console.error('用户登录失败:', error.message);
  }
}

// 测试管理员登录
async function testAdminLogin() {
  try {
    console.log('测试管理员登录...');
    const result = await adminLogin('admin', '123456');
    console.log('管理员登录成功:', result);
  } catch (error) {
    console.error('管理员登录失败:', error.message);
  }
}

// 运行测试
testUserLogin().then(() => {
  testAdminLogin().finally(() => {
    // 关闭数据库连接
    connection.end();
  });
});
