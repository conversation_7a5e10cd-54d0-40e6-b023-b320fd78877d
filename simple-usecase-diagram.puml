@startuml 新闻发布管理系统简化用例图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
}
skinparam usecase {
    BackgroundColor #FFF8E1
    BorderColor #F57C00
    FontColor #E65100
}

title 新闻发布管理系统简化用例图

' 定义参与者
actor "普通用户" as User
actor "管理员" as Admin

' 定义系统边界
rectangle "新闻发布管理系统" {
    ' 主要用例
    usecase "浏览新闻" as UC1
    usecase "查看新闻详情" as UC2
    usecase "按分类筛选" as UC3
    usecase "查看公告" as UC4
    usecase "发表评论" as UC5
    usecase "用户登录" as UC6
    usecase "用户注册" as UC7
    
    usecase "管理员登录" as UC8
    usecase "新闻管理" as UC9
    usecase "分类管理" as UC10
    usecase "公告管理" as UC11
    usecase "评论管理" as UC12
    usecase "用户管理" as UC13
}

' 关联关系
User --> UC1
User --> UC2
User --> UC3
User --> UC4
User --> UC5
User --> UC6
User --> UC7

Admin --> UC8
Admin --> UC9
Admin --> UC10
Admin --> UC11
Admin --> UC12
Admin --> UC13

' 管理员继承用户权限
Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4

' 扩展关系
UC5 ..> UC6 : <<extend>>

@enduml
