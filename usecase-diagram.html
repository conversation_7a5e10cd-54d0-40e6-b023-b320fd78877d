<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻发布管理系统用例图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        .description {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>新闻发布管理系统用例图</h1>
        
        <div class="description">
            <h3>系统概述</h3>
            <p>本系统是一个新闻发布管理系统，包含普通用户和管理员两种角色。普通用户可以浏览新闻、查看公告、发表评论等；管理员可以管理新闻、分类、公告、评论和用户。</p>
        </div>

        <div class="diagram-container">
            <div class="mermaid">
graph TB
    %% 参与者定义
    User[👤 普通用户<br/>User]
    Admin[👨‍💼 管理员<br/>Administrator]
    
    %% 系统边界
    subgraph "新闻发布管理系统"
        %% 用户功能用例
        subgraph "用户功能模块"
            UC1[浏览新闻列表]
            UC2[查看新闻详情]
            UC3[按分类筛选新闻]
            UC4[查看公告列表]
            UC5[查看公告详情]
            UC6[发表评论]
            UC7[用户登录]
            UC8[用户注册]
        end
        
        %% 管理员功能用例
        subgraph "管理员功能模块"
            UC9[管理员登录]
            UC10[新闻管理]
            UC11[分类管理]
            UC12[公告管理]
            UC13[评论管理]
            UC14[用户管理]
        end
        
        %% 新闻管理子用例
        subgraph "新闻管理子功能"
            UC10_1[创建新闻]
            UC10_2[编辑新闻]
            UC10_3[删除新闻]
            UC10_4[发布新闻]
        end
        
        %% 分类管理子用例
        subgraph "分类管理子功能"
            UC11_1[添加分类]
            UC11_2[编辑分类]
            UC11_3[删除分类]
        end
        
        %% 公告管理子用例
        subgraph "公告管理子功能"
            UC12_1[创建公告]
            UC12_2[编辑公告]
            UC12_3[删除公告]
        end
        
        %% 评论管理子用例
        subgraph "评论管理子功能"
            UC13_1[审核评论]
            UC13_2[删除评论]
        end
    end
    
    %% 用户与用例的关联
    User --> UC1
    User --> UC2
    User --> UC3
    User --> UC4
    User --> UC5
    User --> UC6
    User --> UC7
    User --> UC8
    
    %% 管理员与用例的关联
    Admin --> UC9
    Admin --> UC10
    Admin --> UC11
    Admin --> UC12
    Admin --> UC13
    Admin --> UC14
    
    %% 管理员也可以使用用户功能
    Admin -.-> UC1
    Admin -.-> UC2
    Admin -.-> UC3
    Admin -.-> UC4
    Admin -.-> UC5
    
    %% 包含关系 (include)
    UC10 --> UC10_1
    UC10 --> UC10_2
    UC10 --> UC10_3
    UC10 --> UC10_4
    
    UC11 --> UC11_1
    UC11 --> UC11_2
    UC11 --> UC11_3
    
    UC12 --> UC12_1
    UC12 --> UC12_2
    UC12 --> UC12_3
    
    UC13 --> UC13_1
    UC13 --> UC13_2
    
    %% 扩展关系 (extend)
    UC6 -.->|extend| UC7
    
    %% 样式定义
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef adminClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef usecaseClass fill:#fff3e0,stroke:#e65100,stroke-width:1px
    classDef subUsecaseClass fill:#f1f8e9,stroke:#33691e,stroke-width:1px
    
    class User userClass
    class Admin adminClass
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13,UC14 usecaseClass
    class UC10_1,UC10_2,UC10_3,UC10_4,UC11_1,UC11_2,UC11_3,UC12_1,UC12_2,UC12_3,UC13_1,UC13_2 subUsecaseClass
            </div>
        </div>

        <div class="description">
            <h3>用例说明</h3>
            <h4>参与者：</h4>
            <ul>
                <li><strong>普通用户</strong>：网站访问者，可以浏览新闻、查看公告、发表评论</li>
                <li><strong>管理员</strong>：系统管理者，拥有所有管理权限</li>
            </ul>
            
            <h4>主要功能：</h4>
            <ul>
                <li><strong>新闻管理</strong>：创建、编辑、删除、发布新闻</li>
                <li><strong>分类管理</strong>：管理新闻分类</li>
                <li><strong>公告管理</strong>：管理系统公告</li>
                <li><strong>评论管理</strong>：审核、删除评论</li>
                <li><strong>用户管理</strong>：管理用户账户</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
