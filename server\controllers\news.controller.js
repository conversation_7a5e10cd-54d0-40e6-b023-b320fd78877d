const db = require('../db')

exports.create = async (req, res) => {
  if (!req.body.title) {
    return res.status(400).send({ message: '标题不能为空' })
  }

  try {
    const [result] = await db.query(
      'INSERT INTO news (title, content, category_id, created_at) VALUES (?, ?, ?, NOW())',
      [req.body.title, req.body.content, req.body.category_id]
    )

    const [rows] = await db.query(
      'SELECT * FROM news WHERE news_id = ?',
      [result.insertId]
    )
    res.send(rows[0])
  } catch (err) {
    res.status(500).send({ message: err.message || '创建新闻时出错' })
  }
}

exports.findAll = async (req, res) => {
  try {
    // 获取分页参数
    const page = parseInt(req.query.page) || 1
    const size = parseInt(req.query.size) || 10
    const offset = (page - 1) * size

    // 构建查询条件
    let whereClause = ''
    const params = []

    // 按分类筛选
    if (req.query.category) {
      whereClause = 'WHERE n.category_id = ?'
      params.push(parseInt(req.query.category))
    }

    // 获取总记录数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news n
      ${whereClause}
    `
    const [countResult] = await db.query(countQuery, params)
    const totalItems = countResult[0].total

    // 计算总页数
    const totalPages = Math.ceil(totalItems / size)

    // 获取分页数据
    const query = `
      SELECT n.*, c.category_name
      FROM news n
      LEFT JOIN categories c ON n.category_id = c.category_id
      ${whereClause}
      ORDER BY n.created_at DESC
      LIMIT ? OFFSET ?
    `

    // 添加分页参数
    const queryParams = [...params, size, offset]

    const [rows] = await db.query(query, queryParams)

    // 返回结果
    res.send({
      data: rows,
      page,
      size,
      totalItems,
      totalPages
    })
  } catch (err) {
    console.error('获取新闻列表失败:', err)
    res.status(500).send({
      message: err.message || '获取新闻列表时出错'
    })
  }
}

exports.findOne = async (req, res) => {
  const id = req.params.id

  try {
    const [rows] = await db.query(
      `SELECT n.*, c.category_name
       FROM news n
       LEFT JOIN categories c ON n.category_id = c.category_id
       WHERE n.news_id = ?`,
      [id]
    )

    if (rows.length > 0) {
      res.send(rows[0])
    } else {
      res.status(404).send({
        message: `找不到ID为${id}的新闻`
      })
    }
  } catch (err) {
    res.status(500).send({
      message: `获取ID为${id}的新闻时出错: ${err.message}`
    })
  }
}

exports.update = async (req, res) => {
  const id = req.params.id

  try {
    const [result] = await db.query(
      `UPDATE news SET
       title = ?,
       content = ?,
       category_id = ?,
       updated_at = NOW()
       WHERE news_id = ?`,
      [req.body.title, req.body.content, req.body.category_id, id]
    )

    if (result.affectedRows === 1) {
      res.send({ message: '新闻更新成功' })
    } else {
      res.send({
        message: `无法更新ID为${id}的新闻，可能新闻不存在或请求体为空`
      })
    }
  } catch (err) {
    res.status(500).send({
      message: `更新ID为${id}的新闻时出错: ${err.message}`
    })
  }
}

exports.delete = async (req, res) => {
  const id = req.params.id

  try {
    const [result] = await db.query(
      'DELETE FROM news WHERE news_id = ?',
      [id]
    )

    if (result.affectedRows === 1) {
      res.send({ message: '新闻删除成功' })
    } else {
      res.send({
        message: `无法删除ID为${id}的新闻，可能新闻不存在`
      })
    }
  } catch (err) {
    res.status(500).send({
      message: `无法删除ID为${id}的新闻: ${err.message}`
    })
  }
}
