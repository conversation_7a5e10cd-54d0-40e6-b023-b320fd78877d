<template>
  <el-container class="admin-container">
    <el-aside width="200px" class="admin-sidebar">
      <div class="admin-header">
        <h3>管理后台</h3>
      </div>
      <el-menu
        router
        background-color="#545c64"
        text-color="#fff"
        active-text-color="#ffd04b"
        class="admin-menu"
      >
        <el-menu-item index="/admin/news">
          <i class="el-icon-news"></i>
          <span>新闻管理</span>
        </el-menu-item>
        <el-menu-item index="/admin/categories">
          <i class="el-icon-menu"></i>
          <span>栏目管理</span>
        </el-menu-item>
        <el-menu-item index="/admin/announcements">
          <i class="el-icon-message"></i>
          <span>公告管理</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <el-container>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
export default {
  name: 'AdminDashboard'
}
</script>

<style scoped>
.admin-container {
  height: 100vh;
}

.admin-sidebar {
  background-color: #545c64;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.admin-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #434a50;
  color: white;
}

.admin-menu {
  border-right: none;
  flex: 1;
}

.el-main {
  padding: 20px;
  background-color: #f0f2f5;
}
</style>
