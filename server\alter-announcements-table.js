const db = require('./db')

async function alterAnnouncementsTable() {
  try {
    console.log('正在修改公告表结构...')
    
    // 检查updated_at字段是否已存在
    const [columns] = await db.query('SHOW COLUMNS FROM announcements')
    const hasUpdatedAt = columns.some(col => col.Field === 'updated_at')
    
    if (hasUpdatedAt) {
      console.log('updated_at字段已存在，无需添加')
    } else {
      // 添加updated_at字段
      await db.query('ALTER TABLE announcements ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL')
      console.log('成功添加updated_at字段')
    }
    
    // 显示修改后的表结构
    const [newColumns] = await db.query('SHOW COLUMNS FROM announcements')
    console.log('\n修改后的公告表结构:')
    newColumns.forEach(col => {
      console.log(`${col.Field}: ${col.Type} ${col.Null === 'YES' ? '可为空' : '不可为空'} ${col.Key} ${col.Default ? `默认值: ${col.Default}` : ''}`)
    })
    
    console.log('\n表结构修改完成')
  } catch (err) {
    console.error('修改公告表结构时出错:', err)
  } finally {
    process.exit()
  }
}

alterAnnouncementsTable()
