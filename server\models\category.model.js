module.exports = (sequelize, Sequelize) => {
  const Category = sequelize.define('category', {
    category_id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    category_name: {
      type: Sequelize.STRING(100),
      allowNull: false,
      unique: true
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW
    }
  }, {
    timestamps: false,
    tableName: 'categories'
  })

  return Category
}
