const express = require('express')
const router = express.Router()
const authJwt = require('../middleware/authJwt')
const controller = require('../controllers/category.controller')

// 设置CORS头
router.use(function (req, res, next) {
  // 允许多个来源
  const allowedOrigins = ['http://localhost:8080', 'http://localhost:8081'];
  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header(
    'Access-Control-Allow-Headers',
    'x-access-token, Authorization, Origin, Content-Type, Accept'
  );
  res.header('Access-Control-Allow-Credentials', 'true');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    return res.status(200).send();
  }

  next();
})

// 公共路由
router.get('/categories', controller.findAll)

// 管理员权限路由
router.post(
  '/categories',
  [authJwt.verifyToken, authJwt.isAdmin],
  controller.create
)

// 管理员权限路由
router.put(
  '/categories/:id',
  [authJwt.verifyToken, authJwt.isAdmin],
  controller.update
)

// 管理员权限路由
router.delete(
  '/categories/:id',
  [authJwt.verifyToken, authJwt.isAdmin],
  controller.delete
)

module.exports = router
