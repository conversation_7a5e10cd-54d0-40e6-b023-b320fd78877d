<template>
  <div class="news-form">
    <h2>{{ formTitle }}</h2>
    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label>标题</label>
        <input v-model="form.title" type="text" required>
      </div>
      <div class="form-group">
        <label>栏目</label>
        <select v-model="form.category_id" required>
          <option
            v-for="category in categories"
            :key="category.category_id"
            :value="category.category_id"
          >
            {{ category.category_name }}
          </option>
        </select>
      </div>
      <div class="form-group">
        <label>内容</label>
        <textarea v-model="form.content" rows="10" required></textarea>
      </div>
      <button type="submit" :disabled="loading">
        {{ loading ? '提交中...' : '提交' }}
      </button>
      <router-link to="/admin/news" class="btn-cancel">取消</router-link>
    </form>
  </div>
</template>

<script>
export default {
  props: {
    id: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      form: {
        title: '',
        content: '',
        category_id: ''
      },
      categories: [],
      loading: false
    }
  },
  computed: {
    formTitle () {
      return this.id ? '编辑新闻' : '添加新闻'
    }
  },
  created () {
    this.fetchCategories()
    if (this.id) {
      this.fetchNews()
    }
  },
  methods: {
    async fetchCategories () {
      try {
        const res = await this.$http.get('/api/categories')
        this.categories = res.data
      } catch (err) {
        console.error('获取栏目列表失败:', err)
      }
    },
    async fetchNews () {
      try {
        const res = await this.$http.get(`/api/news/${this.id}`)
        this.form = res.data
      } catch (err) {
        console.error('获取新闻详情失败:', err)
      }
    },
    async submitForm () {
      this.loading = true

      try {
        if (this.id) {
          await this.$http.put(`/api/news/${this.id}`, this.form)
        } else {
          await this.$http.post('/api/news', this.form)
        }
        this.$router.push('/admin/news')
      } catch (err) {
        console.error('提交失败:', err)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.news-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}
.form-group {
  margin-bottom: 20px;
}
.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.form-group textarea {
  min-height: 200px;
}
button {
  padding: 10px 15px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}
button:disabled {
  background: #ccc;
}
.btn-cancel {
  padding: 10px 15px;
  background: #f56c6c;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}
</style>
