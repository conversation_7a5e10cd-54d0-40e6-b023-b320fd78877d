/**
 * 数据库初始化脚本
 * 用于创建必要的表和初始数据
 */
const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  user: 'root',
  password: '123456',
  multipleStatements: true
};

// 创建数据库和表的SQL
const createDatabaseSQL = `
CREATE DATABASE IF NOT EXISTS news DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE news;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  user_id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 管理员表
CREATE TABLE IF NOT EXISTS admin_users (
  admin_id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 栏目表
CREATE TABLE IF NOT EXISTS categories (
  category_id INT AUTO_INCREMENT PRIMARY KEY,
  category_name VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 新闻表
CREATE TABLE IF NOT EXISTS news (
  news_id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT,
  category_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(category_id)
);

-- 公告表
CREATE TABLE IF NOT EXISTS announcements (
  announcement_id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
);
`;

// 初始数据
const initDataSQL = `
-- 添加默认管理员账号
INSERT INTO admin_users (username, password)
VALUES ('admin', ?)
ON DUPLICATE KEY UPDATE username = 'admin';

-- 添加默认用户账号
INSERT INTO users (username, password)
VALUES ('testuser', ?)
ON DUPLICATE KEY UPDATE username = 'testuser';

-- 添加默认栏目
INSERT INTO categories (category_name)
VALUES ('政治'), ('经济'), ('科技'), ('文化'), ('体育')
ON DUPLICATE KEY UPDATE category_name = VALUES(category_name);
`;

async function initDatabase() {
  let connection;

  try {
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 创建数据库和表
    console.log('开始创建数据库和表...');
    await connection.query(createDatabaseSQL);
    console.log('数据库和表创建成功');

    // 生成密码哈希
    const adminPasswordHash = bcrypt.hashSync('123456', 8);
    const userPasswordHash = bcrypt.hashSync('123456', 8);

    // 插入初始数据
    console.log('开始插入初始数据...');
    await connection.query(initDataSQL, [adminPasswordHash, userPasswordHash]);
    console.log('初始数据插入成功');

    console.log('数据库初始化完成！');
    console.log('默认管理员账号: admin / 123456');
    console.log('默认用户账号: testuser / 123456');

  } catch (err) {
    console.error('数据库初始化失败:', err);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行初始化
initDatabase();
