<template>
  <div class="category-management">
    <h2>栏目管理</h2>
    <el-button type="primary" @click="showAddDialog">添加栏目</el-button>

    <el-table :data="categories" border style="width: 100%; margin-top: 20px;">
      <el-table-column prop="category_id" label="ID"></el-table-column>
      <el-table-column prop="category_name" label="栏目名称"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="mini" @click="editCategory(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteCategory(scope.row.category_id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="栏目名称" prop="category_name">
          <el-input v-model="form.category_name"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      categories: [],
      dialogVisible: false,
      dialogTitle: '添加栏目',
      form: {
        category_id: null,
        category_name: ''
      },
      rules: {
        category_name: [{ required: true, message: '请输入栏目名称', trigger: 'blur' }]
      }
    }
  },
  created () {
    this.fetchCategories()
  },
  methods: {
    async fetchCategories () {
      try {
        console.log('正在获取栏目列表...')
        const res = await this.$http.get('/categories')
        console.log('栏目列表响应:', res)

        // 处理响应数据
        if (res.data && (Array.isArray(res.data.data) || Array.isArray(res.data))) {
          this.categories = res.data.data || res.data
        } else {
          console.error('栏目数据格式不正确:', res.data)
          this.categories = []
        }
      } catch (err) {
        console.error('获取栏目列表失败:', err)
        this.$message.error('获取栏目列表失败，请检查服务器连接')
      }
    },
    showAddDialog () {
      this.dialogTitle = '添加栏目'
      this.form = {
        category_id: null,
        category_name: ''
      }
      this.dialogVisible = true
    },
    editCategory (category) {
      this.dialogTitle = '编辑栏目'
      this.form = {
        category_id: category.category_id,
        category_name: category.category_name
      }
      this.dialogVisible = true
    },
    async deleteCategory (id) {
      try {
        // 使用ElementUI的确认对话框
        await this.$confirm('确定要删除这个栏目吗？删除后不可恢复，且可能影响关联的新闻。', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        console.log('正在删除栏目:', id)
        await this.$http.delete(`/categories/${id}`)
        this.$message.success('栏目删除成功')
        this.fetchCategories()
      } catch (err) {
        if (err !== 'cancel') {
          console.error('删除栏目失败:', err)
          this.$message.error('删除栏目失败，可能有关联的新闻或服务器连接问题')
        }
      }
    },
    submitForm () {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return

        try {
          if (this.form.category_id) {
            // 更新栏目
            console.log('正在更新栏目:', this.form)
            await this.$http.put(`/categories/${this.form.category_id}`, {
              category_name: this.form.category_name
            })
            this.$message.success('更新成功')
          } else {
            // 添加栏目
            console.log('正在添加栏目:', this.form)
            await this.$http.post('/categories', {
              category_name: this.form.category_name
            })
            this.$message.success('添加成功')
          }
          this.dialogVisible = false
          this.fetchCategories()
        } catch (err) {
          console.error('提交栏目表单失败:', err)
          this.$message.error('操作失败，请检查表单数据或服务器连接')
        }
      })
    }
  }
}
</script>

<style scoped>
.category-management {
  padding: 20px;
}
</style>
