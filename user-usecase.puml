@startuml 用户功能用例图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E8F4FD
    BorderColor #2196F3
    FontColor #1976D2
    FontSize 14
}
skinparam usecase {
    BackgroundColor #FFF9C4
    BorderColor #FF9800
    FontColor #E65100
    FontSize 12
}
skinparam rectangle {
    BackgroundColor #E8F5E8
    BorderColor #4CAF50
    FontColor #2E7D32
}

title 新闻发布管理系统 - 用户功能用例图

' 参与者
:普通用户: as User

' 系统边界
rectangle "用户功能模块" {
    
    ' 主要用例
    (浏览新闻列表) as Browse<PERSON>ews
    (查看新闻详情) as ViewNewsDetail
    (按分类筛选) as FilterByCategory
    (搜索新闻) as SearchNews
    (查看公告) as ViewAnnouncement
    (发表评论) as PostComment
    (用户注册) as UserRegister
    (用户登录) as UserLogin
    
    ' 扩展用例
    (身份验证) as Authentication
    (输入验证) as InputValidation
}

' 用户关联
User --> BrowseNews
User --> ViewNewsDetail
User --> FilterByCategory
User --> SearchNews
User --> ViewAnnouncement
User --> PostComment
User --> UserRegister
User --> UserLogin

' Extend关系 (扩展关系)
Authentication .> PostComment : <<extend>>
Authentication .> UserLogin : <<extend>>
InputValidation .> UserRegister : <<extend>>
InputValidation .> PostComment : <<extend>>

' Include关系
ViewNewsDetail .> BrowseNews : <<include>>

note right of User
  普通用户可以：
  - 浏览和查看新闻
  - 按分类筛选内容
  - 查看系统公告
  - 注册和登录
  - 发表评论（需登录）
end note

@enduml
