const dbConfig = require('../config/db.config.js')
const Sequelize = require('sequelize')

const sequelize = new Sequelize(
  dbConfig.DB,
  dbConfig.USER,
  dbConfig.PASSWORD,
  {
    host: dbConfig.HOST,
    dialect: dbConfig.dialect,
    pool: {
      max: dbConfig.pool.max,
      min: dbConfig.pool.min,
      acquire: dbConfig.pool.acquire,
      idle: dbConfig.pool.idle
    }
  }
)

const db = {}

db.Sequelize = Sequelize
db.sequelize = sequelize

db.user = require('./user.model.js')(sequelize, Sequelize)
db.news = require('./news.model.js')(sequelize, Sequelize)
db.category = require('./category.model.js')(sequelize, Sequelize)
db.admin_users = require('./admin_users.model.js')(sequelize, Sequelize)

// 建立关联关系
db.news.belongsTo(db.category, { foreignKey: 'category_id' })
db.category.hasMany(db.news, { foreignKey: 'category_id' })

module.exports = db
