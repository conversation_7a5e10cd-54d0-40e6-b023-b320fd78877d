const express = require('express')
const cors = require('cors')
const bodyParser = require('body-parser')
const mysql = require('mysql2')
const authRoutes = require('./server/routes/auth.routes')

const app = express()

// 中间件
app.use(cors())
app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: true }))

// 数据库连接
const conn = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'news',
  multipleStatements: true
})

conn.connect(err => {
  if (err) {
    console.error('数据库连接失败:', err)
    return
  }
  console.log('数据库连接成功')
})

// 路由
// 认证路由
app.use('/api', authRoutes)

// 栏目管理
app.get('/api/categories', (req, res) => {
  conn.query('SELECT * FROM categories', (err, results) => {
    if (err) return res.status(500).json({ code: 500, message: err.message })
    res.json({ code: 200, data: results })
  })
})

app.post('/api/categories', (req, res) => {
  const { category_name: categoryName } = req.body
  conn.query('INSERT INTO categories SET ?', { category_name: categoryName }, (err, result) => {
    if (err) return res.status(500).json({ code: 500, message: err.message })
    res.json({ code: 200, data: { id: result.insertId }, message: '添加成功' })
  })
})

app.put('/api/categories/:id', (req, res) => {
  const id = req.params.id
  const { category_name: categoryName } = req.body
  conn.query('UPDATE categories SET ? WHERE category_id = ?',
    [{ category_name: categoryName }, id],
    (err, result) => {
      if (err) return res.status(500).json({ code: 500, message: err.message })
      res.json({ code: 200, message: '更新成功' })
    })
})

app.delete('/api/categories/:id', (req, res) => {
  const id = req.params.id
  conn.query('DELETE FROM categories WHERE category_id = ?', id, (err, result) => {
    if (err) return res.status(500).json({ code: 500, message: err.message })
    res.json({ code: 200, message: '删除成功' })
  })
})

// 新闻管理
app.get('/api/news', (req, res) => {
  let query = 'SELECT n.*, c.category_name FROM news n LEFT JOIN categories c ON n.category_id = c.category_id'
  const params = []

  if (req.query.category_id) {
    query += ' WHERE n.category_id = ?'
    params.push(req.query.category_id)
  }

  conn.query(query, params, (err, results) => {
    if (err) return res.status(500).json({ code: 500, message: err.message })
    res.json({ code: 200, data: results })
  })
})

// 启动服务器
const PORT = 3001
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`)
})
