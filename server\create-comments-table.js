/**
 * 创建评论表
 */
const db = require('./db');

async function createCommentsTable() {
  try {
    console.log('开始创建评论表...');

    // 先删除表（如果存在）
    try {
      await db.query('DROP TABLE IF EXISTS comments');
      console.log('已删除旧的评论表');
    } catch (dropErr) {
      console.error('删除旧表失败:', dropErr);
    }

    // 创建评论表
    await db.query(`
      CREATE TABLE IF NOT EXISTS comments (
        comment_id INT AUTO_INCREMENT PRIMARY KEY,
        news_id INT NOT NULL,
        user_id INT NOT NULL,
        username VARCHAR(50) NOT NULL,
        comment_content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (news_id) REFERENCES news(news_id) ON DELETE CASCADE
      )
    `);

    // 验证表结构
    const [columns] = await db.query('SHOW COLUMNS FROM comments');
    console.log('评论表结构:', columns.map(col => col.Field).join(', '));

    console.log('评论表创建成功！');

    // 添加一些测试评论
    console.log('添加测试评论...');

    // 获取所有新闻ID
    const [newsRows] = await db.query('SELECT news_id FROM news');

    if (newsRows.length > 0) {
      // 为每个新闻添加一些测试评论
      for (const news of newsRows) {
        await db.query(
          'INSERT INTO comments (news_id, user_id, username, comment_content, created_at) VALUES (?, ?, ?, ?, NOW())',
          [news.news_id, 1, 'admin', '这是一条测试评论']
        );

        await db.query(
          'INSERT INTO comments (news_id, user_id, username, comment_content, created_at) VALUES (?, ?, ?, ?, NOW())',
          [news.news_id, 2, 'testuser', '这篇新闻很有意思']
        );
      }

      console.log('测试评论添加成功！');
    } else {
      console.log('没有找到新闻，跳过添加测试评论');
    }

    // 查询评论数量
    const [countResult] = await db.query('SELECT COUNT(*) as count FROM comments');
    console.log(`评论表中现有 ${countResult[0].count} 条记录`);

  } catch (err) {
    console.error('创建评论表失败:', err);
  } finally {
    process.exit();
  }
}

// 执行创建评论表
createCommentsTable();
