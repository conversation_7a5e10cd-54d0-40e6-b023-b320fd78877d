<template>
  <div class="news-list">
    <h1>新闻列表</h1>

    <!-- 分类筛选 -->
    <div class="category-filter">
      <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
        <el-radio-button :label="0">全部</el-radio-button>
        <el-radio-button v-for="category in categories" :key="category.category_id" :label="category.category_id">
          {{ category.category_name }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 新闻列表 -->
    <div v-if="filteredNews.length > 0">
      <div v-for="news in filteredNews" :key="news.news_id" class="news-item">
        <router-link :to="`/news/${news.news_id}`">
          <h2>{{ news.title }}</h2>
        </router-link>
        <p class="meta">
          <span class="date">{{ formatDate(news.created_at) }}</span>
          <span class="category-tag" :style="{ backgroundColor: getCategoryColor(news.category_id) }">
            {{ news.category_name }}
          </span>
        </p>
        <p class="summary" v-if="news.content">{{ truncateContent(news.content) }}</p>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="no-data">
      <el-empty description="暂无相关新闻"></el-empty>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <el-pagination
        @current-change="handlePageChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="totalItems">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      newsList: [],
      categories: [],
      selectedCategory: 0, // 0表示全部
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      pageSize: 10,
      categoryColors: {
        1: '#f56c6c', // 政治 - 红色
        2: '#67c23a', // 经济 - 绿色
        3: '#409eff', // 科技 - 蓝色
        4: '#e6a23c', // 文化 - 黄色
        5: '#909399' // 体育 - 灰色
      }
    }
  },
  computed: {
    filteredNews () {
      if (this.selectedCategory === 0) {
        return this.newsList
      } else {
        return this.newsList.filter(news => news.category_id === this.selectedCategory)
      }
    }
  },
  created () {
    this.fetchCategories()
    this.fetchNews()
  },
  methods: {
    async fetchCategories () {
      try {
        console.log('正在获取栏目列表...')
        const res = await this.$http.get('/categories')
        console.log('栏目列表响应:', res.data)

        if (res.data && Array.isArray(res.data)) {
          this.categories = res.data
        } else {
          console.error('栏目数据格式不正确:', res.data)
          // 使用模拟数据作为备份
          this.categories = [
            { category_id: 1, category_name: '政治' },
            { category_id: 2, category_name: '经济' },
            { category_id: 3, category_name: '科技' },
            { category_id: 4, category_name: '文化' },
            { category_id: 5, category_name: '体育' }
          ]
        }
      } catch (err) {
        console.error('获取栏目列表失败:', err)
        // 使用模拟数据作为备份
        this.categories = [
          { category_id: 1, category_name: '政治' },
          { category_id: 2, category_name: '经济' },
          { category_id: 3, category_name: '科技' },
          { category_id: 4, category_name: '文化' },
          { category_id: 5, category_name: '体育' }
        ]
      }
    },
    async fetchNews () {
      try {
        console.log('正在获取新闻列表...')
        // 构建查询参数
        let url = `/news?page=${this.currentPage}&size=${this.pageSize}`
        if (this.selectedCategory !== 0) {
          url += `&category=${this.selectedCategory}`
        }

        const res = await this.$http.get(url)
        console.log('新闻列表响应:', res.data)

        // 处理响应数据
        if (res.data && Array.isArray(res.data.data || res.data)) {
          this.newsList = res.data.data || res.data
          this.totalPages = res.data.totalPages || 1
          this.totalItems = res.data.totalItems || this.newsList.length
        } else {
          console.error('新闻数据格式不正确:', res.data)
          // 使用模拟数据作为备份
          this.newsList = [
            {
              news_id: 1,
              title: '国家重大政策发布',
              content: '国家发布了一系列重大政策，涉及经济、教育、医疗等多个领域...',
              category_id: 1,
              category_name: '政治',
              created_at: '2023-05-15 10:00:00'
            },
            {
              news_id: 2,
              title: '经济增长率达到新高',
              content: '今年第一季度经济增长率达到8.5%，超出预期...',
              category_id: 2,
              category_name: '经济',
              created_at: '2023-05-14 14:30:00'
            },
            {
              news_id: 3,
              title: '新型人工智能技术突破',
              content: '科研人员在人工智能领域取得重大突破，新算法效率提升50%...',
              category_id: 3,
              category_name: '科技',
              created_at: '2023-05-13 09:15:00'
            }
          ]
          this.totalItems = this.newsList.length
          this.totalPages = 1
        }
      } catch (err) {
        console.error('获取新闻列表失败:', err)
        // 使用模拟数据作为备份
        this.newsList = [
          {
            news_id: 1,
            title: '国家重大政策发布',
            content: '国家发布了一系列重大政策，涉及经济、教育、医疗等多个领域...',
            category_id: 1,
            category_name: '政治',
            created_at: '2023-05-15 10:00:00'
          },
          {
            news_id: 2,
            title: '经济增长率达到新高',
            content: '今年第一季度经济增长率达到8.5%，超出预期...',
            category_id: 2,
            category_name: '经济',
            created_at: '2023-05-14 14:30:00'
          },
          {
            news_id: 3,
            title: '新型人工智能技术突破',
            content: '科研人员在人工智能领域取得重大突破，新算法效率提升50%...',
            category_id: 3,
            category_name: '科技',
            created_at: '2023-05-13 09:15:00'
          }
        ]
        this.totalItems = this.newsList.length
        this.totalPages = 1
      }
    },
    handlePageChange (page) {
      this.currentPage = page
      this.fetchNews()
    },
    handleCategoryChange () {
      this.currentPage = 1 // 切换分类时重置为第一页
      this.fetchNews()
    },
    getCategoryColor (categoryId) {
      return this.categoryColors[categoryId] || '#909399' // 默认灰色
    },
    formatDate (dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })
    },
    truncateContent (content) {
      if (!content) return ''
      return content.length > 100 ? content.substring(0, 100) + '...' : content
    }
  }
}
</script>

<style scoped>
.news-list {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  text-align: center;
  color: #303133;
}

.category-filter {
  margin-bottom: 30px;
  text-align: center;
}

.news-item {
  margin-bottom: 25px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
}

.news-item h2 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.news-item a {
  text-decoration: none;
  color: inherit;
}

.meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.date {
  color: #909399;
  font-size: 14px;
}

.category-tag {
  padding: 2px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
}

.summary {
  color: #606266;
  line-height: 1.6;
  margin-top: 10px;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

.no-data {
  margin-top: 50px;
  text-align: center;
}
</style>
