<template>
  <div class="announcement-management">
    <h2>公告管理</h2>
    <el-button type="primary" @click="goToAddAnnouncement">添加公告</el-button>

    <el-table :data="announcements" border style="width: 100%; margin-top: 20px;">
      <el-table-column prop="announcement_id" label="ID"></el-table-column>
      <el-table-column prop="title" label="标题"></el-table-column>
      <el-table-column label="发布时间">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="mini" @click="goToEditAnnouncement(scope.row.announcement_id)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteAnnouncement(scope.row.announcement_id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data () {
    return {
      announcements: []
    }
  },
  created () {
    this.fetchAnnouncements()
  },
  methods: {
    async fetchAnnouncements () {
      try {
        console.log('正在获取公告列表...')
        const res = await this.$http.get('/announcements')
        console.log('公告列表响应:', res)

        // 处理响应数据
        if (res.data && (Array.isArray(res.data.data) || Array.isArray(res.data))) {
          this.announcements = res.data.data || res.data
        } else {
          console.error('公告数据格式不正确:', res.data)
          this.announcements = []
        }
      } catch (err) {
        console.error('获取公告列表失败:', err)
        this.$message.error('获取公告列表失败，请检查服务器连接')
      }
    },
    formatDate (dateString) {
      return new Date(dateString).toLocaleString()
    },
    goToAddAnnouncement () {
      this.$router.push('/admin/announcements/create')
    },
    goToEditAnnouncement (id) {
      this.$router.push(`/admin/announcements/edit/${id}`)
    },
    async deleteAnnouncement (id) {
      try {
        // 使用ElementUI的确认对话框
        await this.$confirm('确定要删除这条公告吗？删除后不可恢复。', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        console.log('正在删除公告:', id)
        await this.$http.delete(`/announcements/${id}`)
        this.$message.success('公告删除成功')
        this.fetchAnnouncements()
      } catch (err) {
        if (err !== 'cancel') {
          console.error('删除公告失败:', err)
          this.$message.error('删除公告失败，请检查服务器连接')
        }
      }
    }
  }
}
</script>

<style scoped>
.announcement-management {
  padding: 20px;
}
</style>
