const db = require('../db')
const jwt = require('jsonwebtoken')
const bcrypt = require('bcryptjs')
const config = require('../config/auth.config')

exports.userLogin = async (req, res) => {
  try {
    console.log('用户登录请求:', req.body);

    if (!req.body.username || !req.body.password) {
      return res.status(400).send({ message: '用户名和密码不能为空' });
    }

    // 直接从数据库查询用户
    const [rows] = await db.query(
      'SELECT * FROM users WHERE username = ? LIMIT 1',
      [req.body.username]
    );

    console.log('查询结果:', rows);

    if (rows.length === 0) {
      return res.status(404).send({ message: '用户不存在' });
    }

    const user = rows[0];

    // 兼容明文和加密密码
    let passwordIsValid;
    if (user.password && (user.password.startsWith('$2a$') || user.password.startsWith('$2b$'))) {
      passwordIsValid = bcrypt.compareSync(req.body.password, user.password);
    } else {
      passwordIsValid = req.body.password === user.password;
    }

    console.log('密码验证结果:', passwordIsValid);

    if (!passwordIsValid) {
      return res.status(401).send({
        accessToken: null,
        message: '密码错误'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.user_id, role: 'user' },
      config.secret,
      { expiresIn: 86400 } // 24小时
    );

    // 返回用户信息和令牌
    res.status(200).send({
      id: user.user_id,
      username: user.username,
      role: 'user',
      accessToken: token
    });
  } catch (err) {
    console.error('用户登录错误:', err);
    res.status(500).send({ message: '服务器错误，请稍后再试' });
  }
}

exports.adminLogin = async (req, res) => {
  try {
    console.log('管理员登录请求:', req.body);

    if (!req.body.username || !req.body.password) {
      return res.status(400).send({ message: '用户名和密码不能为空' });
    }

    // 直接从数据库查询管理员
    const [rows] = await db.query(
      'SELECT * FROM admin_users WHERE username = ? LIMIT 1',
      [req.body.username]
    );

    console.log('查询结果:', rows);

    if (rows.length === 0) {
      return res.status(404).send({ message: '管理员不存在' });
    }

    const admin = rows[0];

    // 兼容明文和加密密码
    let passwordIsValid;
    if (admin.password && (admin.password.startsWith('$2a$') || admin.password.startsWith('$2b$'))) {
      passwordIsValid = bcrypt.compareSync(req.body.password, admin.password);
    } else {
      passwordIsValid = req.body.password === admin.password;
    }

    console.log('管理员密码验证结果:', passwordIsValid);

    if (!passwordIsValid) {
      return res.status(401).send({
        accessToken: null,
        message: '密码错误'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { id: admin.admin_id, role: 'admin' },
      config.secret,
      { expiresIn: 86400 } // 24小时
    );

    // 返回管理员信息和令牌
    res.status(200).send({
      id: admin.admin_id,
      username: admin.username,
      role: 'admin',
      accessToken: token
    });
  } catch (err) {
    console.error('管理员登录错误:', err);
    res.status(500).send({ message: '服务器错误，请稍后再试' });
  }
}

exports.logout = (req, res) => {
  res.status(200).send({ message: '登出成功' })
}
