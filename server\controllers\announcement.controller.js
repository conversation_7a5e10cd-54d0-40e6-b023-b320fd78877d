const db = require('../db')

exports.create = async (req, res) => {
  if (!req.body.title || !req.body.content) {
    return res.status(400).send({ message: '标题和内容不能为空' })
  }

  try {
    const [result] = await db.query(
      'INSERT INTO announcements (title, content, created_at) VALUES (?, ?, NOW())',
      [req.body.title, req.body.content]
    )
    res.send({ announcement_id: result.insertId, ...req.body })
  } catch (err) {
    res.status(500).send({ message: err.message || '创建公告时出错' })
  }
}

exports.findAll = async (req, res) => {
  try {
    const [rows] = await db.query('SELECT * FROM announcements ORDER BY created_at DESC')
    res.send(rows)
  } catch (err) {
    res.status(500).send({ message: err.message || '获取公告列表时出错' })
  }
}

exports.findOne = async (req, res) => {
  const id = req.params.id

  try {
    const [rows] = await db.query(
      'SELECT * FROM announcements WHERE announcement_id = ?',
      [id]
    )

    if (rows.length > 0) {
      res.send(rows[0])
    } else {
      res.status(404).send({
        message: `找不到ID为${id}的公告`
      })
    }
  } catch (err) {
    res.status(500).send({
      message: `获取ID为${id}的公告时出错: ${err.message}`
    })
  }
}

exports.update = async (req, res) => {
  const id = req.params.id

  if (!req.body.title || !req.body.content) {
    return res.status(400).send({ message: '标题和内容不能为空' })
  }

  try {
    const [result] = await db.query(
      `UPDATE announcements SET
       title = ?,
       content = ?
       WHERE announcement_id = ?`,
      [req.body.title, req.body.content, id]
    )

    if (result.affectedRows === 1) {
      res.send({ message: '公告更新成功' })
    } else {
      res.send({
        message: `无法更新ID为${id}的公告，可能公告不存在`
      })
    }
  } catch (err) {
    res.status(500).send({
      message: `更新ID为${id}的公告时出错: ${err.message}`
    })
  }
}

exports.delete = async (req, res) => {
  const id = req.params.id

  try {
    const [result] = await db.query(
      'DELETE FROM announcements WHERE announcement_id = ?',
      [id]
    )

    if (result.affectedRows === 1) {
      res.send({ message: '公告删除成功' })
    } else {
      res.send({ message: `无法删除ID为${id}的公告，可能公告不存在` })
    }
  } catch (err) {
    res.status(500).send({ message: `删除公告时出错: ${err.message}` })
  }
}
