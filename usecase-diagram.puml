@startuml 新闻发布管理系统用例图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
}
skinparam usecase {
    BackgroundColor #FFF8E1
    BorderColor #F57C00
    FontColor #E65100
}

title 新闻发布管理系统用例图

' 定义参与者
actor "普通用户" as User
actor "管理员" as Admin

' 定义系统边界
rectangle "新闻发布管理系统" {
    ' 用户功能用例
    usecase "浏览新闻列表" as UC1
    usecase "查看新闻详情" as UC2
    usecase "按分类筛选新闻" as UC3
    usecase "查看公告列表" as UC4
    usecase "查看公告详情" as UC5
    usecase "发表评论" as UC6
    usecase "用户登录" as UC7
    usecase "用户注册" as UC8
    
    ' 管理员功能用例
    usecase "管理员登录" as UC9
    usecase "新闻管理" as UC10
    usecase "分类管理" as UC11
    usecase "公告管理" as UC12
    usecase "评论管理" as UC13
    usecase "用户管理" as UC14
    
    ' 新闻管理子用例
    usecase "创建新闻" as UC10_1
    usecase "编辑新闻" as UC10_2
    usecase "删除新闻" as UC10_3
    usecase "发布新闻" as UC10_4
    
    ' 分类管理子用例
    usecase "添加分类" as UC11_1
    usecase "编辑分类" as UC11_2
    usecase "删除分类" as UC11_3
    
    ' 公告管理子用例
    usecase "创建公告" as UC12_1
    usecase "编辑公告" as UC12_2
    usecase "删除公告" as UC12_3
    
    ' 评论管理子用例
    usecase "审核评论" as UC13_1
    usecase "删除评论" as UC13_2
}

' 用户关联
User --> UC1
User --> UC2
User --> UC3
User --> UC4
User --> UC5
User --> UC6
User --> UC7
User --> UC8

' 管理员关联
Admin --> UC9
Admin --> UC10
Admin --> UC11
Admin --> UC12
Admin --> UC13
Admin --> UC14

' 管理员继承用户权限
Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4
Admin --> UC5

' 包含关系
UC10 ..> UC10_1 : <<include>>
UC10 ..> UC10_2 : <<include>>
UC10 ..> UC10_3 : <<include>>
UC10 ..> UC10_4 : <<include>>

UC11 ..> UC11_1 : <<include>>
UC11 ..> UC11_2 : <<include>>
UC11 ..> UC11_3 : <<include>>

UC12 ..> UC12_1 : <<include>>
UC12 ..> UC12_2 : <<include>>
UC12 ..> UC12_3 : <<include>>

UC13 ..> UC13_1 : <<include>>
UC13 ..> UC13_2 : <<include>>

' 扩展关系
UC6 ..> UC7 : <<extend>>

@enduml
