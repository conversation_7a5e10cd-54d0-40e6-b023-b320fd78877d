@startuml 新闻管理系统标准UML用例图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E8F4FD
    BorderColor #2196F3
    FontColor #1976D2
    FontSize 12
}
skinparam usecase {
    BackgroundColor #FFF9C4
    BorderColor #FF9800
    FontColor #E65100
    FontSize 11
}
skinparam rectangle {
    BackgroundColor #F3E5F5
    BorderColor #9C27B0
    FontColor #7B1FA2
}

title 新闻发布管理系统 - 标准UML用例图

' 参与者 - 使用标准小人图标
:普通用户: as User
:管理员: as Admin

' 系统边界
rectangle "新闻发布管理系统" {
    
    ' 用户主要用例
    (浏览新闻) as <PERSON><PERSON><PERSON>
    (查看详情) as ViewDetail
    (筛选新闻) as Filter
    (查看公告) as ViewAnnouncement
    (发表评论) as Comment
    (用户登录) as UserLogin
    
    ' 管理员主要用例
    (管理员登录) as <PERSON><PERSON><PERSON>og<PERSON>
    (新闻管理) as NewsManage
    (分类管理) as CategoryManage
    (公告管理) as AnnouncementManage
    (评论管理) as CommentManage
    
    ' 新闻管理的子用例 (include关系)
    (创建新闻) as CreateNews
    (编辑新闻) as EditNews
    (删除新闻) as DeleteNews
    (发布新闻) as PublishNews
    
    ' 分类管理的子用例 (include关系)
    (添加分类) as AddCategory
    (编辑分类) as EditCategory
    (删除分类) as DeleteCategory
    
    ' 公告管理的子用例 (include关系)
    (创建公告) as CreateAnnouncement
    (编辑公告) as EditAnnouncement
    (删除公告) as DeleteAnnouncement
    
    ' 评论管理的子用例 (include关系)
    (审核评论) as ReviewComment
    (删除评论) as DeleteComment
    
    ' 身份验证用例 (extend关系)
    (身份验证) as Authentication
}

' 用户关联
User --> Browse
User --> ViewDetail
User --> Filter
User --> ViewAnnouncement
User --> Comment
User --> UserLogin

' 管理员关联
Admin --> AdminLogin
Admin --> NewsManage
Admin --> CategoryManage
Admin --> AnnouncementManage
Admin --> CommentManage

' 管理员也可以使用用户功能
Admin --> Browse
Admin --> ViewDetail
Admin --> ViewAnnouncement

' Include关系 (包含关系)
NewsManage .> CreateNews : <<include>>
NewsManage .> EditNews : <<include>>
NewsManage .> DeleteNews : <<include>>
NewsManage .> PublishNews : <<include>>

CategoryManage .> AddCategory : <<include>>
CategoryManage .> EditCategory : <<include>>
CategoryManage .> DeleteCategory : <<include>>

AnnouncementManage .> CreateAnnouncement : <<include>>
AnnouncementManage .> EditAnnouncement : <<include>>
AnnouncementManage .> DeleteAnnouncement : <<include>>

CommentManage .> ReviewComment : <<include>>
CommentManage .> DeleteComment : <<include>>

' Extend关系 (扩展关系)
Authentication .> Comment : <<extend>>
Authentication .> UserLogin : <<extend>>
Authentication .> AdminLogin : <<extend>>

@enduml
