/**
 * 直接使用MySQL进行登录验证的路由
 * 这个文件提供了直接使用MySQL进行用户和管理员登录验证的路由
 */
const express = require('express');
const mysql = require('mysql2');
const jwt = require('jsonwebtoken');
const config = require('./config/auth.config');

const router = express.Router();

// 创建数据库连接池
const pool = mysql.createPool({
  host: '127.0.0.1',
  user: 'root',
  password: '123456',
  database: 'news',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 获取连接池的Promise包装
const promisePool = pool.promise();

/**
 * 用户登录路由
 */
router.post('/user/login', async (req, res) => {
  try {
    console.log('用户登录请求:', req.body);
    
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码不能为空' });
    }
    
    // 查询用户
    const [rows] = await promisePool.query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );
    
    console.log('查询结果:', rows);
    
    if (rows.length === 0) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    const user = rows[0];
    
    // 验证密码（这里假设密码是明文存储的）
    if (user.password !== password) {
      return res.status(401).json({
        message: '密码错误'
      });
    }
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.user_id, role: 'user' },
      config.secret,
      { expiresIn: 86400 } // 24小时
    );
    
    // 返回用户信息和令牌
    res.status(200).json({
      id: user.user_id,
      username: user.username,
      role: 'user',
      accessToken: token
    });
  } catch (err) {
    console.error('用户登录错误:', err);
    res.status(500).json({ message: '服务器错误，请稍后再试' });
  }
});

/**
 * 管理员登录路由
 */
router.post('/admin/login', async (req, res) => {
  try {
    console.log('管理员登录请求:', req.body);
    
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码不能为空' });
    }
    
    // 查询管理员
    const [rows] = await promisePool.query(
      'SELECT * FROM admin_users WHERE username = ?',
      [username]
    );
    
    console.log('查询结果:', rows);
    
    if (rows.length === 0) {
      return res.status(404).json({ message: '管理员不存在' });
    }
    
    const admin = rows[0];
    
    // 验证密码（这里假设密码是明文存储的）
    if (admin.password !== password) {
      return res.status(401).json({
        message: '密码错误'
      });
    }
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: admin.admin_id, role: 'admin' },
      config.secret,
      { expiresIn: 86400 } // 24小时
    );
    
    // 返回管理员信息和令牌
    res.status(200).json({
      id: admin.admin_id,
      username: admin.username,
      role: 'admin',
      accessToken: token
    });
  } catch (err) {
    console.error('管理员登录错误:', err);
    res.status(500).json({ message: '服务器错误，请稍后再试' });
  }
});

/**
 * 登出路由
 */
router.post('/logout', (req, res) => {
  res.status(200).json({ message: '登出成功' });
});

module.exports = router;
