import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/news'
  },
  {
    path: '/login',
    name: 'user-login',
    component: () => import(/* webpackChunkName: "user" */ '../views/UserLogin.vue'),
    meta: { guest: true }
  },
  {
    path: '/news',
    name: 'news',
    component: () => import(/* webpackChunkName: "news" */ '../views/NewsList.vue')
  },
  {
    path: '/news/:id',
    name: 'news-detail',
    component: () => import(/* webpackChunkName: "news" */ '../views/NewsDetail.vue')
  },
  {
    path: '/announcements',
    name: 'announcements',
    component: () => import(/* webpackChunkName: "announcements" */ '../views/AnnouncementList.vue')
  },
  {
    path: '/announcements/:id',
    name: 'announcement-detail',
    component: () => import(/* webpackChunkName: "announcements" */ '../views/AnnouncementDetail.vue')
  },
  {
    path: '/admin/login',
    name: 'admin-login',
    component: () => import(/* webpackChunkName: "admin" */ '../views/AdminLogin.vue'),
    meta: { guest: true }
  },
  {
    path: '/admin',
    name: 'admin',
    component: () => import(/* webpackChunkName: "admin" */ '../views/AdminDashboard.vue'),
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: 'news',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/NewsManagement.vue')
      },
      {
        path: 'news/create',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/NewsForm.vue')
      },
      {
        path: 'news/edit/:id',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/NewsForm.vue'),
        props: true
      },
      {
        path: 'categories',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/CategoryManagement.vue')
      },
      {
        path: 'announcements',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/AnnouncementManagement.vue')
      },
      {
        path: 'announcements/create',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/AnnouncementForm.vue')
      },
      {
        path: 'announcements/edit/:id',
        component: () => import(/* webpackChunkName: "admin" */ '../views/admin/AnnouncementForm.vue'),
        props: true
      }
    ]
  },
  {
    path: '/about',
    name: 'about',
    component: () => import(/* webpackChunkName: "about" */ '../views/AboutView.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach((to, from, next) => {
  const isAuthenticated = !!localStorage.getItem('userToken')
  const isAdmin = !!localStorage.getItem('adminToken')
  const isGuestRoute = to.matched.some(record => record.meta.guest)
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  if (isGuestRoute && (isAuthenticated || isAdmin)) {
    // 已登录用户访问登录页面时重定向到首页
    next('/')
  } else if (requiresAuth && !isAuthenticated && !isAdmin) {
    // 未登录用户访问需要认证的页面时重定向到登录页面
    next('/login')
  } else if (requiresAdmin && !isAdmin) {
    // 非管理员访问管理员页面时重定向到管理员登录页面
    next('/admin/login')
  } else {
    next()
  }
})

export default router
