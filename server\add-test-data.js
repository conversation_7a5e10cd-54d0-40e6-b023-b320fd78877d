/**
 * 添加测试数据
 */
const db = require('./db')

async function addTestData () {
  try {
    console.log('开始添加测试数据...')

    // 添加测试新闻
    console.log('添加测试新闻...')
    const newsData = [
      {
        title: '国家重大政策发布',
        content: '国家发布了一系列重大政策，涉及经济、教育、医疗等多个领域...',
        category_id: 1
      },
      {
        title: '经济增长率达到新高',
        content: '今年第一季度经济增长率达到8.5%，超出预期...',
        category_id: 2
      },
      {
        title: '新型人工智能技术突破',
        content: '科研人员在人工智能领域取得重大突破，新算法效率提升50%...',
        category_id: 3
      }
    ]
    for (const news of newsData) {
      await db.query(
        'INSERT INTO news (title, content, category_id, created_at) VALUES (?, ?, ?, NOW())',
        [news.title, news.content, news.category_id]
      )
    }

    // 添加测试公告
    console.log('添加测试公告...')
    const announcementData = [
      {
        title: '网站维护通知',
        content: '本网站将于2023年6月1日进行系统维护，届时可能无法访问...'
      },
      {
        title: '用户注册功能上线',
        content: '用户注册功能已正式上线，欢迎注册使用...'
      }
    ]
    for (const announcement of announcementData) {
      await db.query(
        'INSERT INTO announcements (title, content, created_at) VALUES (?, ?, NOW())',
        [announcement.title, announcement.content]
      )
    }

    console.log('测试数据添加成功！')

    // 查询添加的数据
    const [news] = await db.query('SELECT * FROM news')
    console.log(`新闻表中现有 ${news.length} 条记录`)

    const [announcements] = await db.query('SELECT * FROM announcements')
    console.log(`公告表中现有 ${announcements.length} 条记录`)
  } catch (err) {
    console.error('添加测试数据失败:', err)
  } finally {
    process.exit()
  }
}

// 执行添加测试数据
addTestData()
