import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    categories: [],
    user: null,
    admin: null
  },
  getters: {
    getCategories: state => state.categories,
    getUser: state => state.user,
    getAdmin: state => state.admin,
    isUserLoggedIn: state => !!state.user,
    isAdminLoggedIn: state => !!state.admin
  },
  mutations: {
    SET_CATEGORIES (state, categories) {
      state.categories = categories || []
    },
    setUser (state, user) {
      state.user = user
    },
    setAdmin (state, admin) {
      state.admin = admin
    },
    // 清除用户状态
    clearUser (state) {
      state.user = null
    },
    // 清除管理员状态
    clearAdmin (state) {
      state.admin = null
    },
    // 清除所有状态
    clearAll (state) {
      state.user = null
      state.admin = null
    }
  },
  actions: {
    // 初始化状态 - 从localStorage加载
    initializeStore ({ commit }) {
      // 尝试从localStorage加载用户信息
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          commit('setUser', JSON.parse(userInfo))
        }

        const adminInfo = localStorage.getItem('adminInfo')
        if (adminInfo) {
          commit('setAdmin', JSON.parse(adminInfo))
        }
      } catch (error) {
        console.error('初始化状态失败:', error)
      }
    },

    // 获取分类列表
    async fetchCategories ({ commit }) {
      try {
        // 使用模拟数据，避免API请求错误
        const mockCategories = [
          { category_id: 1, category_name: '政治' },
          { category_id: 2, category_name: '经济' },
          { category_id: 3, category_name: '科技' },
          { category_id: 4, category_name: '文化' },
          { category_id: 5, category_name: '体育' }
        ]
        commit('SET_CATEGORIES', mockCategories)
      } catch (error) {
        console.error('获取栏目列表失败:', error)
      }
    },

    // 登出
    logout ({ commit }) {
      localStorage.removeItem('userToken')
      localStorage.removeItem('adminToken')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('adminInfo')
      commit('clearAll')
    }
  }
})
