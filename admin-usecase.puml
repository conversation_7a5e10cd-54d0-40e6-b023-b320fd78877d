@startuml 管理员功能用例图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #FCE4EC
    BorderColor #E91E63
    FontColor #C2185B
    FontSize 14
}
skinparam usecase {
    BackgroundColor #FFF3E0
    BorderColor #FF9800
    FontColor #E65100
    FontSize 12
}
skinparam rectangle {
    BackgroundColor #F3E5F5
    BorderColor #9C27B0
    FontColor #7B1FA2
}

title 新闻发布管理系统 - 管理员功能用例图

' 参与者
:管理员: as Admin

' 系统边界
rectangle "管理员功能模块" {
    
    ' 主要管理用例
    (管理员登录) as Admin<PERSON>ogin
    (新闻管理) as NewsManagement
    (分类管理) as CategoryManagement
    (公告管理) as AnnouncementManagement
    (评论管理) as CommentManagement
    (用户管理) as UserManagement
    (系统设置) as SystemSettings
    
    ' 新闻管理子用例
    (创建新闻) as CreateNews
    (编辑新闻) as EditNews
    (删除新闻) as DeleteNews
    (发布新闻) as PublishNews
    (新闻审核) as ReviewNews
    
    ' 分类管理子用例
    (添加分类) as AddCategory
    (编辑分类) as EditCategory
    (删除分类) as DeleteCategory
    (分类排序) as SortCategory
    
    ' 公告管理子用例
    (创建公告) as CreateAnnouncement
    (编辑公告) as EditAnnouncement
    (删除公告) as DeleteAnnouncement
    (发布公告) as PublishAnnouncement
    
    ' 评论管理子用例
    (审核评论) as ReviewComment
    (删除评论) as DeleteComment
    (回复评论) as ReplyComment
    
    ' 用户管理子用例
    (查看用户) as ViewUsers
    (禁用用户) as DisableUser
    (重置密码) as ResetPassword
    
    ' 权限验证
    (权限验证) as PermissionCheck
}

' 管理员关联
Admin --> AdminLogin
Admin --> NewsManagement
Admin --> CategoryManagement
Admin --> AnnouncementManagement
Admin --> CommentManagement
Admin --> UserManagement
Admin --> SystemSettings

' Include关系 - 新闻管理
NewsManagement .> CreateNews : <<include>>
NewsManagement .> EditNews : <<include>>
NewsManagement .> DeleteNews : <<include>>
NewsManagement .> PublishNews : <<include>>
NewsManagement .> ReviewNews : <<include>>

' Include关系 - 分类管理
CategoryManagement .> AddCategory : <<include>>
CategoryManagement .> EditCategory : <<include>>
CategoryManagement .> DeleteCategory : <<include>>
CategoryManagement .> SortCategory : <<include>>

' Include关系 - 公告管理
AnnouncementManagement .> CreateAnnouncement : <<include>>
AnnouncementManagement .> EditAnnouncement : <<include>>
AnnouncementManagement .> DeleteAnnouncement : <<include>>
AnnouncementManagement .> PublishAnnouncement : <<include>>

' Include关系 - 评论管理
CommentManagement .> ReviewComment : <<include>>
CommentManagement .> DeleteComment : <<include>>
CommentManagement .> ReplyComment : <<include>>

' Include关系 - 用户管理
UserManagement .> ViewUsers : <<include>>
UserManagement .> DisableUser : <<include>>
UserManagement .> ResetPassword : <<include>>

' Extend关系 - 权限验证
PermissionCheck .> NewsManagement : <<extend>>
PermissionCheck .> CategoryManagement : <<extend>>
PermissionCheck .> AnnouncementManagement : <<extend>>
PermissionCheck .> CommentManagement : <<extend>>
PermissionCheck .> UserManagement : <<extend>>
PermissionCheck .> SystemSettings : <<extend>>

note right of Admin
  管理员权限：
  - 完整的内容管理权限
  - 用户和评论管理
  - 系统配置权限
  - 所有操作需权限验证
end note

@enduml
