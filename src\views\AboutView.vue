<template>
  <div class="about">
    <h1>关于我们</h1>
    <div class="content">
      <p>欢迎来到新闻网！</p>
      <p>本系统是一个基于Vue.js和Node.js开发的新闻网平台，旨在提供便捷的新闻发布、管理和浏览功能。</p>

      <h2>系统功能</h2>
      <ul>
        <li>新闻浏览：用户可以浏览最新新闻，查看新闻详情</li>
        <li>新闻分类：新闻按照不同栏目进行分类，方便用户查找</li>
        <li>评论功能：用户可以对新闻发表评论，参与讨论</li>
        <li>公告查看：用户可以查看系统公告</li>
        <li>新闻管理：管理员可以进行新闻的增删查改</li>
        <li>栏目管理：管理员可以管理新闻栏目</li>
        <li>公告管理：管理员可以发布和管理系统公告</li>
      </ul>

      <h2>技术栈</h2>
      <h3>前端</h3>
      <ul>
        <li>Vue.js 2</li>
        <li>Element UI</li>
        <li>Vuex</li>
        <li>Vue Router</li>
        <li>Axios</li>
      </ul>
      <h3>后端</h3>
      <ul>
        <li>Node.js</li>
        <li>Express.js</li>
        <li>MySQL</li>
        <li>JWT</li>
      </ul>

      <h2>联系我们</h2>
      <p>如果您有任何问题或建议，请联系我们：</p>
      <p>邮箱：<EMAIL></p>
      <p>电话：123-456-7890</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AboutView'
}
</script>

<style scoped>
.about {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #333;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

h2 {
  color: #409EFF;
  margin-top: 30px;
  margin-bottom: 15px;
}

h3 {
  color: #67C23A;
  margin-top: 20px;
}

.content {
  line-height: 1.6;
}

ul {
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
}

p {
  margin-bottom: 15px;
}
</style>
