<script>
export default {
  computed: {
    isUserLoggedIn () {
      return this.$store.getters.isUserLoggedIn || !!localStorage.getItem('userToken')
    },
    isAdminLoggedIn () {
      return this.$store.getters.isAdminLoggedIn || !!localStorage.getItem('adminToken')
    }
  },
  methods: {
    logout () {
      // 使用Vuex的logout action
      this.$store.dispatch('logout')

      // 跳转到登录页面
      this.$router.push('/login').catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          console.warn('导航错误:', err)
        }
      })

      this.$message.success('已成功登出')
    }
  }
}
</script>

<template>
  <div id="app">
    <el-menu
      mode="horizontal"
      background-color="#545c64"
      text-color="#fff"
      active-text-color="#ffd04b">
      <el-menu-item index="/news">
        <router-link to="/news">新闻列表</router-link>
      </el-menu-item>
      <el-menu-item index="/announcements">
        <router-link to="/announcements">公告列表</router-link>
      </el-menu-item>
      <el-menu-item index="/about">
        <router-link to="/about">关于我们</router-link>
      </el-menu-item>
      <template v-if="!isUserLoggedIn && !isAdminLoggedIn">
        <el-menu-item index="/login">
          <router-link to="/login">用户登录</router-link>
        </el-menu-item>
        <el-menu-item index="/admin/login">
          <router-link to="/admin/login">管理员登录</router-link>
        </el-menu-item>
      </template>
      <template v-else>
        <el-menu-item v-if="isAdminLoggedIn" index="/admin">
          <router-link to="/admin">管理后台</router-link>
        </el-menu-item>
        <el-menu-item @click="logout">登出</el-menu-item>
      </template>
    </el-menu>
    <router-view/>
  </div>
</template>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-menu-item a {
  color: inherit;
  text-decoration: none;
  display: block;
}
</style>
