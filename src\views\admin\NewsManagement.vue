<template>
  <div class="news-management">
    <h2>新闻管理</h2>
    <el-button type="primary" @click="showAddDialog">添加新闻</el-button>

    <el-table :data="newsList" border style="width: 100%">
      <el-table-column prop="news_id" label="ID"></el-table-column>
      <el-table-column prop="title" label="标题"></el-table-column>
      <el-table-column prop="category_name" label="栏目"></el-table-column>
      <el-table-column prop="created_at" label="发布时间"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row.news_id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input type="textarea" v-model="form.content"></el-input>
        </el-form-item>
        <el-form-item label="栏目" prop="category_id">
          <el-select v-model="form.category_id" placeholder="请选择栏目">
            <el-option
              v-for="category in categories"
              :key="category.category_id"
              :label="category.category_name"
              :value="category.category_id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      newsList: [],
      categories: [],
      dialogVisible: false,
      dialogTitle: '添加新闻',
      form: {
        news_id: null,
        title: '',
        content: '',
        category_id: null
      },
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        category_id: [{ required: true, message: '请选择栏目', trigger: 'change' }]
      }
    }
  },
  created () {
    this.fetchNews()
    this.fetchCategories()
  },
  methods: {
    async fetchNews () {
      try {
        console.log('正在获取新闻列表...')
        const res = await this.$http.get('/news')
        console.log('新闻列表响应:', res)

        // 处理响应数据
        if (res.data && (Array.isArray(res.data.data) || Array.isArray(res.data))) {
          this.newsList = res.data.data || res.data
        } else {
          console.error('新闻数据格式不正确:', res.data)
          this.newsList = []
        }
      } catch (err) {
        console.error('获取新闻列表失败:', err)
        this.$message.error('获取新闻列表失败，请检查服务器连接')
      }
    },
    async fetchCategories () {
      try {
        console.log('正在获取栏目列表...')
        const res = await this.$http.get('/categories')
        console.log('栏目列表响应:', res)

        // 处理响应数据
        if (res.data && (Array.isArray(res.data.data) || Array.isArray(res.data))) {
          this.categories = res.data.data || res.data
        } else {
          console.error('栏目数据格式不正确:', res.data)
          this.categories = []
        }
      } catch (err) {
        console.error('获取栏目列表失败:', err)
        this.$message.error('获取栏目列表失败，请检查服务器连接')
      }
    },
    showAddDialog () {
      this.dialogTitle = '添加新闻'
      this.form = {
        news_id: null,
        title: '',
        content: '',
        category_id: null
      }
      this.dialogVisible = true
    },
    handleEdit (row) {
      this.dialogTitle = '编辑新闻'
      this.form = {
        news_id: row.news_id,
        title: row.title,
        content: row.content,
        category_id: row.category_id
      }
      this.dialogVisible = true
    },
    async handleDelete (id) {
      try {
        await this.$confirm('确定删除这条新闻吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        console.log('正在删除新闻:', id)
        await this.$http.delete(`/news/${id}`)
        this.$message.success('删除成功')
        this.fetchNews()
      } catch (err) {
        if (err !== 'cancel') {
          console.error('删除新闻失败:', err)
          this.$message.error('删除失败，请检查权限或服务器连接')
        }
      }
    },
    submitForm () {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return

        try {
          if (this.form.news_id) {
            // 更新新闻
            console.log('正在更新新闻:', this.form)
            await this.$http.put(`/news/${this.form.news_id}`, this.form)
            this.$message.success('更新成功')
          } else {
            // 添加新闻
            console.log('正在添加新闻:', this.form)
            await this.$http.post('/news', this.form)
            this.$message.success('添加成功')
          }
          this.dialogVisible = false
          this.fetchNews()
        } catch (err) {
          console.error('提交新闻表单失败:', err)
          this.$message.error('操作失败，请检查表单数据或服务器连接')
        }
      })
    }
  }
}
</script>

<style scoped>
.news-management {
  padding: 20px;
}
</style>
