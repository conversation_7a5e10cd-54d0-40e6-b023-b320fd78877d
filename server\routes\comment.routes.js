const express = require('express')
const router = express.Router()
const authJwt = require('../middleware/authJwt')
const controller = require('../controllers/comment.controller')

// 设置CORS头
router.use(function (req, res, next) {
  // 允许多个来源
  const allowedOrigins = ['http://localhost:8080', 'http://localhost:8081'];
  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }
  
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header(
    'Access-Control-Allow-Headers',
    'x-access-token, Authorization, Origin, Content-Type, Accept'
  );
  res.header('Access-Control-Allow-Credentials', 'true');
  
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    return res.status(200).send();
  }
  
  next();
})

// 获取新闻的评论列表
router.get('/news/:newsId/comments', controller.findAll)

// 添加评论（需要登录）
router.post(
  '/news/:newsId/comments',
  [authJwt.verifyToken],
  controller.create
)

// 删除评论（需要是评论作者或管理员）
router.delete(
  '/news/:newsId/comments/:commentId',
  [authJwt.verifyToken],
  controller.delete
)

module.exports = router
