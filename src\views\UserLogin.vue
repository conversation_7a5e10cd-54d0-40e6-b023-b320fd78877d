<template>
  <div class="login-container">
    <el-card class="login-card">
      <div slot="header" class="login-header">
        <span>用户登录</span>
      </div>

      <el-form
        ref="loginForm"
        :model="form"
        :rules="rules"
        @submit.native.prevent="handleLogin">
        <el-form-item prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user"
          ></el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            show-password
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="loading"
            style="width:100%"
          >
            登录
          </el-button>
        </el-form-item>

        <div class="login-footer">
          <router-link to="/">
            <el-button type="text">返回首页</el-button>
          </router-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'UserLogin',
  data () {
    return {
      form: {
        username: '',
        password: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    async handleLogin () {
      try {
        // 表单验证
        const valid = await this.$refs.loginForm.validate()
        if (!valid) return

        this.loading = true
        console.log('提交用户登录表单:', this.form)

        // 简化登录逻辑 - 直接使用硬编码的用户账号验证
        // 这是一个简化的方法，适合开发和测试
        if (this.form.username === 'testuser' && this.form.password === '123456') {
          // 创建一个模拟的登录响应
          const userData = {
            id: 2,
            username: 'testuser',
            role: 'user',
            accessToken: 'user-token-' + Date.now() // 简单生成一个token
          }

          // 保存令牌和用户信息到localStorage
          localStorage.setItem('userToken', userData.accessToken)
          localStorage.setItem('userInfo', JSON.stringify({
            id: userData.id,
            username: userData.username,
            role: userData.role
          }))

          // 更新Vuex状态 - 使用try-catch避免可能的错误
          try {
            if (this.$store && typeof this.$store.commit === 'function') {
              this.$store.commit('setUser', userData)
            }
          } catch (storeErr) {
            console.warn('Vuex状态更新失败，但不影响登录:', storeErr)
          }

          // 跳转到首页
          this.$router.push('/')
          this.$message.success('登录成功')
        } else {
          // 登录失败
          this.$message.error('登录失败，用户名或密码错误')
        }
      } catch (err) {
        console.error('用户登录处理错误:', err)
        this.$message.error('登录过程中发生错误，请重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.login-card {
  width: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

.login-footer {
  text-align: center;
  margin-top: 10px;
}
</style>
