@startuml 简化标准UML用例图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
    FontSize 14
}
skinparam usecase {
    BackgroundColor #FFF8E1
    BorderColor #F57C00
    FontColor #E65100
    FontSize 12
}
skinparam rectangle {
    BackgroundColor #F1F8E9
    BorderColor #689F38
    FontColor #33691E
}

title 新闻发布管理系统 - 简化UML用例图

' 参与者 - 标准小人图标
:普通用户: as User
:管理员: as Admin

' 系统边界
rectangle "新闻发布管理系统" {
    
    ' 核心用例
    (浏览新闻) as <PERSON><PERSON><PERSON>
    (查看详情) as Detail
    (发表评论) as Comment
    (用户登录) as <PERSON><PERSON>
    
    (内容管理) as ContentMgmt
    (用户管理) as UserMgmt
    (管理员登录) as AdminLogin
    
    ' 子用例
    (创建内容) as Create
    (编辑内容) as Edit
    (删除内容) as Delete
    
    (审核) as Review
    (权限验证) as Auth
}

' 关联关系
User --> Browse
User --> Detail
User --> Comment
User --> Login

Admin --> ContentMgmt
Admin --> UserMgmt
Admin --> AdminLogin
Admin --> Browse
Admin --> Detail

' Include关系 (必须包含的功能)
ContentMgmt .> Create : <<include>>
ContentMgmt .> Edit : <<include>>
ContentMgmt .> Delete : <<include>>

UserMgmt .> Review : <<include>>

' Extend关系 (可选扩展的功能)
Auth .> Comment : <<extend>>
Auth .> ContentMgmt : <<extend>>
Auth .> UserMgmt : <<extend>>

note top of User
普通用户
end note

note top of Admin
系统管理员
end note

note bottom of ContentMgmt
包含新闻、公告、
分类的管理
end note

@enduml
