# 如何生成用例图图片

我已经为您创建了多种格式的用例图文件，您可以使用以下方法生成图片：

## 文件列表

1. **用例图文档.md** - 包含PlantUML和Mermaid格式的完整文档
2. **usecase-diagram.puml** - PlantUML格式的详细用例图
3. **simple-usecase-diagram.puml** - PlantUML格式的简化用例图
4. **usecase-diagram.html** - 可直接在浏览器中查看的Mermaid图

## 生成图片的方法

### 方法1：使用PlantUML生成图片

#### 在线生成（推荐）
1. 访问 [PlantUML在线编辑器](http://www.plantuml.com/plantuml/uml/)
2. 复制 `usecase-diagram.puml` 文件的内容
3. 粘贴到编辑器中
4. 点击"Submit"按钮
5. 右键点击生成的图片，选择"另存为"保存图片

#### 使用VS Code插件
1. 安装VS Code插件：`PlantUML`
2. 打开 `usecase-diagram.puml` 文件
3. 按 `Alt + D` 预览图表
4. 右键选择"Export Current Diagram"导出图片

#### 使用命令行工具
```bash
# 安装PlantUML
npm install -g node-plantuml

# 生成PNG图片
puml generate usecase-diagram.puml -o usecase-diagram.png

# 生成SVG图片
puml generate usecase-diagram.puml -t svg -o usecase-diagram.svg
```

### 方法2：使用Mermaid生成图片

#### 直接查看HTML文件
1. 双击打开 `usecase-diagram.html` 文件
2. 在浏览器中查看图表
3. 使用浏览器的截图功能或打印功能保存图片

#### 使用Mermaid在线编辑器
1. 访问 [Mermaid在线编辑器](https://mermaid.live/)
2. 复制 `用例图文档.md` 中的Mermaid代码
3. 粘贴到编辑器中
4. 点击"Download PNG"或"Download SVG"下载图片

#### 使用VS Code插件
1. 安装VS Code插件：`Mermaid Preview`
2. 打开包含Mermaid代码的markdown文件
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入"Mermaid: Preview"预览图表
5. 右键选择导出图片

### 方法3：使用截图工具

如果上述方法不可用，您可以：
1. 打开 `usecase-diagram.html` 文件
2. 使用系统截图工具（如Windows的Snipping Tool）
3. 截取图表部分并保存

## 推荐方法

**最简单的方法**：
1. 双击打开 `usecase-diagram.html` 文件
2. 在浏览器中查看完整的用例图
3. 使用浏览器的打印功能，选择"另存为PDF"或截图保存

**最高质量的方法**：
1. 使用PlantUML在线编辑器生成SVG格式图片
2. SVG格式可以无损缩放，适合用于文档和演示

## 图片格式说明

- **PNG**：适合网页显示和一般文档
- **SVG**：矢量格式，适合高质量打印和缩放
- **PDF**：适合正式文档和报告

## 注意事项

1. 如果图片显示不完整，可以调整浏览器窗口大小后重新截图
2. PlantUML生成的图片通常质量更高，更适合正式文档
3. Mermaid图表在浏览器中显示效果更好，交互性更强

现在您可以根据需要选择合适的方法生成用例图图片了！
