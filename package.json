{"name": "vue0", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "core-js": "^3.42.0", "cors": "^2.8.5", "element-ui": "^2.15.14", "express": "^5.1.0", "mysql": "^2.18.1", "mysql2": "^3.14.1", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^7.20.0", "vue-template-compiler": "^2.6.14"}, "description": "``` npm install ```", "main": ".eslintrc.js", "keywords": [], "author": "", "license": "ISC"}