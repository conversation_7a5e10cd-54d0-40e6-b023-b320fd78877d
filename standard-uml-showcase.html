<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准UML用例图展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        .file-info {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .file-name {
            font-weight: bold;
            color: #2980b9;
            font-size: 16px;
        }
        .description {
            margin: 15px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .features {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .features h4 {
            color: #27ae60;
            margin-top: 0;
        }
        .features ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .features li {
            margin: 5px 0;
        }
        .usage-guide {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .usage-guide h3 {
            color: #856404;
            margin-top: 0;
        }
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background-color: #fff2cc;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .relationship-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .relationship-table th,
        .relationship-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .relationship-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .relationship-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 新闻发布管理系统 - 标准UML用例图</h1>
        
        <div class="description">
            <p>我已经为您创建了多个版本的标准UML用例图，每个都有不同的详细程度和用途。所有图表都使用标准的小人图标作为参与者，并清晰地展示了include和extend关系。</p>
        </div>

        <h2>📁 创建的文件列表</h2>

        <div class="file-info">
            <div class="file-name">1. standard-uml-usecase.puml</div>
            <p><strong>标准完整版UML用例图</strong> - 包含所有功能模块，清晰展示include和extend关系</p>
        </div>

        <div class="file-info">
            <div class="file-name">2. user-usecase.puml</div>
            <p><strong>用户功能用例图</strong> - 专门展示普通用户的功能模块</p>
        </div>

        <div class="file-info">
            <div class="file-name">3. admin-usecase.puml</div>
            <p><strong>管理员功能用例图</strong> - 专门展示管理员的功能模块</p>
        </div>

        <div class="file-info">
            <div class="file-name">4. minimal-uml-usecase.puml</div>
            <p><strong>最简化UML用例图</strong> - 核心功能的简洁展示</p>
        </div>

        <h2>🎨 图表特点</h2>

        <div class="features">
            <h4>✅ 标准UML规范</h4>
            <ul>
                <li>使用标准的小人图标 <span class="highlight">:参与者:</span> 表示参与者</li>
                <li>椭圆形表示用例 <span class="highlight">(用例名称)</span></li>
                <li>矩形边界表示系统范围</li>
                <li>实线表示关联关系</li>
                <li>虚线箭头表示include/extend关系</li>
            </ul>
        </div>

        <div class="features">
            <h4>🔗 关系类型说明</h4>
            <table class="relationship-table">
                <tr>
                    <th>关系类型</th>
                    <th>表示方法</th>
                    <th>含义</th>
                    <th>示例</th>
                </tr>
                <tr>
                    <td>关联 (Association)</td>
                    <td>实线 →</td>
                    <td>参与者可以执行的用例</td>
                    <td>用户 → 浏览新闻</td>
                </tr>
                <tr>
                    <td>包含 (Include)</td>
                    <td>虚线箭头 .> <<include>></td>
                    <td>一个用例必须包含另一个用例</td>
                    <td>新闻管理 .> 创建新闻</td>
                </tr>
                <tr>
                    <td>扩展 (Extend)</td>
                    <td>虚线箭头 .> <<extend>></td>
                    <td>在特定条件下扩展功能</td>
                    <td>身份验证 .> 发表评论</td>
                </tr>
            </table>
        </div>

        <h2>🚀 使用方法</h2>

        <div class="usage-guide">
            <h3>方法1：PlantUML在线编辑器（推荐）</h3>
            <ol>
                <li>访问：<a href="http://www.plantuml.com/plantuml/uml/" target="_blank">http://www.plantuml.com/plantuml/uml/</a></li>
                <li>复制任意 .puml 文件的内容</li>
                <li>粘贴到编辑器中</li>
                <li>点击"Submit"生成图片</li>
                <li>右键保存图片（PNG/SVG格式）</li>
            </ol>
        </div>

        <div class="usage-guide">
            <h3>方法2：VS Code插件</h3>
            <ol>
                <li>安装插件：<span class="highlight">PlantUML</span></li>
                <li>打开 .puml 文件</li>
                <li>按 <span class="highlight">Alt + D</span> 预览</li>
                <li>右键选择"Export Current Diagram"</li>
            </ol>
        </div>

        <div class="usage-guide">
            <h3>方法3：命令行工具</h3>
            <div class="code-block">
# 安装PlantUML
npm install -g node-plantuml

# 生成PNG图片
puml generate standard-uml-usecase.puml -o standard-usecase.png

# 生成SVG图片
puml generate standard-uml-usecase.puml -t svg -o standard-usecase.svg
            </div>
        </div>

        <h2>📊 推荐使用场景</h2>

        <div class="features">
            <ul>
                <li><strong>standard-uml-usecase.puml</strong> - 适合完整的系统文档和需求分析</li>
                <li><strong>user-usecase.puml</strong> - 适合用户手册和前端开发参考</li>
                <li><strong>admin-usecase.puml</strong> - 适合管理员培训和后台开发参考</li>
                <li><strong>minimal-uml-usecase.puml</strong> - 适合概要设计和快速展示</li>
            </ul>
        </div>

        <div class="description">
            <h3>💡 提示</h3>
            <p>所有的用例图都严格遵循UML标准，使用小人图标作为参与者，清晰地展示了系统的功能结构和参与者之间的关系。您可以根据不同的需求选择合适的版本使用。</p>
        </div>
    </div>
</body>
</html>
