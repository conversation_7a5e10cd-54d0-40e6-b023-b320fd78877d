<template>
  <div class="announcement-form">
    <h2>{{ formTitle }}</h2>
    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label>标题</label>
        <input v-model="form.title" type="text" required>
      </div>
      <div class="form-group">
        <label>内容</label>
        <textarea v-model="form.content" rows="10" required></textarea>
      </div>
      <button type="submit" :disabled="loading">
        {{ loading ? '提交中...' : '提交' }}
      </button>
      <router-link to="/admin/announcements" class="btn-cancel">取消</router-link>
    </form>
  </div>
</template>

<script>
export default {
  props: {
    id: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      form: {
        title: '',
        content: ''
      },
      loading: false
    }
  },
  computed: {
    formTitle () {
      return this.id ? '编辑公告' : '添加公告'
    }
  },
  created () {
    if (this.id) {
      this.fetchAnnouncement()
    }
  },
  methods: {
    async fetchAnnouncement () {
      try {
        console.log('正在获取公告详情:', this.id)
        const res = await this.$http.get(`/announcements/${this.id}`)
        console.log('公告详情响应:', res)

        if (res.data) {
          this.form = res.data
        } else {
          this.$message.error('获取公告详情失败，数据格式不正确')
        }
      } catch (err) {
        console.error('获取公告详情失败:', err)
        this.$message.error('获取公告详情失败，请检查服务器连接')
      }
    },
    async submitForm () {
      // 表单验证
      if (!this.form.title.trim()) {
        this.$message.error('标题不能为空')
        return
      }

      if (!this.form.content.trim()) {
        this.$message.error('内容不能为空')
        return
      }

      this.loading = true

      try {
        if (this.id) {
          // 更新公告
          console.log('正在更新公告:', this.id, this.form)
          await this.$http.put(`/announcements/${this.id}`, this.form)
          this.$message.success('公告更新成功')
        } else {
          // 添加公告
          console.log('正在添加公告:', this.form)
          await this.$http.post('/announcements', this.form)
          this.$message.success('公告添加成功')
        }
        this.$router.push('/admin/announcements')
      } catch (err) {
        console.error('提交公告失败:', err)
        this.$message.error('提交失败，请检查表单数据或服务器连接')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.announcement-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}
.form-group {
  margin-bottom: 20px;
}
.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.form-group textarea {
  min-height: 200px;
}
button {
  padding: 10px 15px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}
button:disabled {
  background: #ccc;
}
.btn-cancel {
  padding: 10px 15px;
  background: #f56c6c;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}
</style>
