<template>
  <div class="announcement-list">
    <h1>公告列表</h1>

    <el-card v-for="announcement in announcements" :key="announcement.announcement_id" class="announcement-card">
      <div slot="header" class="clearfix">
        <router-link :to="`/announcements/${announcement.announcement_id}`" class="announcement-title">
          {{ announcement.title }}
        </router-link>
        <span class="announcement-date">{{ formatDate(announcement.created_at) }}</span>
      </div>
      <div class="announcement-content">
        {{ truncateContent(announcement.content) }}
        <router-link :to="`/announcements/${announcement.announcement_id}`" class="read-more">
          查看详情
        </router-link>
      </div>
    </el-card>

    <div v-if="announcements.length === 0" class="no-data">
      暂无公告
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      announcements: []
    }
  },
  created () {
    this.fetchAnnouncements()
  },
  methods: {
    async fetchAnnouncements () {
      try {
        console.log('正在获取公告列表...')
        const res = await this.$http.get('/announcements')
        console.log('公告列表响应:', res.data)

        // 处理响应数据
        if (res.data && (Array.isArray(res.data.data) || Array.isArray(res.data))) {
          this.announcements = res.data.data || res.data
        } else {
          console.error('公告数据格式不正确:', res.data)
          this.announcements = []
        }
      } catch (err) {
        console.error('获取公告列表失败:', err)
        this.$message.error('获取公告列表失败，请检查服务器连接')
      }
    },
    formatDate (dateString) {
      return new Date(dateString).toLocaleString()
    },
    truncateContent (content) {
      if (!content) return ''
      return content.length > 100 ? content.substring(0, 100) + '...' : content
    }
  }
}
</script>

<style scoped>
.announcement-list {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.announcement-card {
  margin-bottom: 20px;
}

.announcement-title {
  font-weight: bold;
  font-size: 18px;
  color: #409EFF;
  text-decoration: none;
}

.announcement-title:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.announcement-date {
  float: right;
  color: #999;
  font-size: 14px;
}

.read-more {
  display: block;
  margin-top: 10px;
  color: #409EFF;
  text-decoration: none;
}

.read-more:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.announcement-content {
  margin-top: 10px;
  line-height: 1.6;
  white-space: pre-line;
}

.no-data {
  text-align: center;
  color: #999;
  margin-top: 50px;
}
</style>
