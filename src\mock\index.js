/**
 * 模拟数据服务
 * 用于前端开发，不需要后端API
 */

// 模拟分类数据
export const categories = [
  { category_id: 1, category_name: '政治' },
  { category_id: 2, category_name: '经济' },
  { category_id: 3, category_name: '科技' },
  { category_id: 4, category_name: '文化' },
  { category_id: 5, category_name: '体育' }
]

// 模拟新闻数据
export const news = [
  {
    news_id: 1,
    title: '国家重大政策发布',
    content: '国家发布了一系列重大政策，涉及经济、教育、医疗等多个领域...',
    category_id: 1,
    category_name: '政治',
    created_at: '2023-05-15 10:00:00'
  },
  {
    news_id: 2,
    title: '经济增长率达到新高',
    content: '今年第一季度经济增长率达到8.5%，超出预期...',
    category_id: 2,
    category_name: '经济',
    created_at: '2023-05-14 14:30:00'
  },
  {
    news_id: 3,
    title: '新型人工智能技术突破',
    content: '科研人员在人工智能领域取得重大突破，新算法效率提升50%...',
    category_id: 3,
    category_name: '科技',
    created_at: '2023-05-13 09:15:00'
  },
  {
    news_id: 4,
    title: '传统文化保护计划启动',
    content: '国家启动传统文化保护计划，投入10亿元用于非物质文化遗产保护...',
    category_id: 4,
    category_name: '文化',
    created_at: '2023-05-12 16:45:00'
  },
  {
    news_id: 5,
    title: '国家队获得世界杯冠军',
    content: '我国足球队在世界杯比赛中获得冠军，创造历史...',
    category_id: 5,
    category_name: '体育',
    created_at: '2023-05-11 20:30:00'
  }
]

// 模拟公告数据
export const announcements = [
  {
    announcement_id: 1,
    title: '网站维护通知',
    content: '本网站将于2023年6月1日进行系统维护，届时可能无法访问...',
    created_at: '2023-05-20 08:00:00'
  },
  {
    announcement_id: 2,
    title: '用户注册功能上线',
    content: '用户注册功能已正式上线，欢迎注册使用...',
    created_at: '2023-05-18 14:00:00'
  }
]

// 模拟用户数据
export const users = [
  {
    user_id: 1,
    username: 'testuser',
    password: '123456',
    role: 'user'
  }
]

// 模拟管理员数据
export const admins = [
  {
    admin_id: 1,
    username: 'admin',
    password: '123456',
    role: 'admin'
  }
]

/**
 * 模拟用户登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Object|null} 登录成功返回用户信息，失败返回null
 */
export function mockUserLogin (username, password) {
  const user = users.find(u => u.username === username && u.password === password)
  if (user) {
    return {
      id: user.user_id,
      username: user.username,
      role: user.role,
      accessToken: `user-token-${Date.now()}`
    }
  }
  return null
}

/**
 * 模拟管理员登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Object|null} 登录成功返回管理员信息，失败返回null
 */
export function mockAdminLogin (username, password) {
  const admin = admins.find(a => a.username === username && a.password === password)
  if (admin) {
    return {
      id: admin.admin_id,
      username: admin.username,
      role: admin.role,
      accessToken: `admin-token-${Date.now()}`
    }
  }
  return null
}

/**
 * 获取新闻列表
 * @param {number} categoryId 分类ID（可选）
 * @returns {Array} 新闻列表
 */
export function getNewsList (categoryId) {
  if (categoryId) {
    return news.filter(n => n.category_id === categoryId)
  }
  return news
}

/**
 * 获取新闻详情
 * @param {number} newsId 新闻ID
 * @returns {Object|null} 新闻详情
 */
export function getNewsDetail (newsId) {
  return news.find(n => n.news_id === newsId) || null
}

/**
 * 获取分类列表
 * @returns {Array} 分类列表
 */
export function getCategoryList () {
  return categories
}

/**
 * 获取公告列表
 * @returns {Array} 公告列表
 */
export function getAnnouncementList () {
  return announcements
}

export default {
  mockUserLogin,
  mockAdminLogin,
  getNewsList,
  getNewsDetail,
  getCategoryList,
  getAnnouncementList
}
