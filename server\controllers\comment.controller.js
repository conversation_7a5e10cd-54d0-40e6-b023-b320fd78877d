const db = require('../db')

/**
 * 创建评论
 */
exports.create = async (req, res) => {
  if (!req.body.comment_content) {
    return res.status(400).send({ message: '评论内容不能为空' })
  }

  try {
    // 获取用户信息
    let userId = req.userId
    let username = 'anonymous'

    // 如果是用户登录，获取用户名
    if (req.userRole === 'user') {
      const [userRows] = await db.query(
        'SELECT username FROM users WHERE user_id = ?',
        [userId]
      )
      if (userRows.length > 0) {
        username = userRows[0].username
      }
    } else if (req.userRole === 'admin') {
      // 如果是管理员登录，获取管理员用户名
      const [adminRows] = await db.query(
        'SELECT username FROM admin_users WHERE admin_id = ?',
        [userId]
      )
      if (adminRows.length > 0) {
        username = adminRows[0].username + ' (管理员)'
      }
    }

    // 创建评论
    const [result] = await db.query(
      'INSERT INTO comments (news_id, user_id, username, comment_content, created_at) VALUES (?, ?, ?, ?, NOW())',
      [req.params.newsId, userId, username, req.body.comment_content]
    )

    res.status(201).send({
      message: '评论创建成功',
      comment_id: result.insertId
    })
  } catch (err) {
    console.error('创建评论失败:', err)
    res.status(500).send({ message: err.message || '创建评论时出错' })
  }
}

/**
 * 获取新闻的评论列表
 */
exports.findAll = async (req, res) => {
  try {
    const [rows] = await db.query(
      'SELECT * FROM comments WHERE news_id = ? ORDER BY created_at DESC',
      [req.params.newsId]
    )
    res.send(rows)
  } catch (err) {
    console.error('获取评论列表失败:', err)
    res.status(500).send({ message: err.message || '获取评论列表时出错' })
  }
}

/**
 * 删除评论
 */
exports.delete = async (req, res) => {
  try {
    // 检查是否是评论作者或管理员
    if (req.userRole !== 'admin') {
      const [commentRows] = await db.query(
        'SELECT user_id FROM comments WHERE comment_id = ?',
        [req.params.commentId]
      )
      
      if (commentRows.length === 0) {
        return res.status(404).send({ message: '评论不存在' })
      }
      
      if (commentRows[0].user_id !== req.userId) {
        return res.status(403).send({ message: '只有评论作者或管理员可以删除评论' })
      }
    }
    
    // 删除评论
    const [result] = await db.query(
      'DELETE FROM comments WHERE comment_id = ?',
      [req.params.commentId]
    )
    
    if (result.affectedRows === 0) {
      return res.status(404).send({ message: '评论不存在' })
    }
    
    res.send({ message: '评论删除成功' })
  } catch (err) {
    console.error('删除评论失败:', err)
    res.status(500).send({ message: err.message || '删除评论时出错' })
  }
}
