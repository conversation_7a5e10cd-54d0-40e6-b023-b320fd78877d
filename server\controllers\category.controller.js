const db = require('../db')

exports.create = async (req, res) => {
  if (!req.body.category_name) {
    return res.status(400).send({ message: '栏目名称不能为空' })
  }

  try {
    const [result] = await db.query(
      'INSERT INTO categories (category_name) VALUES (?)',
      [req.body.category_name]
    )
    res.send({ category_id: result.insertId, ...req.body })
  } catch (err) {
    res.status(500).send({ message: err.message || '创建栏目时出错' })
  }
}

exports.findAll = async (req, res) => {
  try {
    const [rows] = await db.query('SELECT * FROM categories')
    res.send(rows)
  } catch (err) {
    res.status(500).send({ message: err.message || '获取栏目列表时出错' })
  }
}

exports.update = async (req, res) => {
  const id = req.params.id

  try {
    const [result] = await db.query(
      'UPDATE categories SET category_name = ? WHERE category_id = ?',
      [req.body.category_name, id]
    )

    if (result.affectedRows === 1) {
      res.send({ message: '栏目更新成功' })
    } else {
      res.send({ message: `无法更新ID为${id}的栏目，可能栏目不存在` })
    }
  } catch (err) {
    res.status(500).send({ message: `更新栏目时出错: ${err.message}` })
  }
}

exports.delete = async (req, res) => {
  const id = req.params.id

  try {
    // 先检查该栏目下是否有新闻
    const [news] = await db.query(
      'SELECT 1 FROM news WHERE category_id = ? LIMIT 1',
      [id]
    )

    if (news.length > 0) {
      return res.status(400).send({ message: '该栏目下存在新闻，无法删除' })
    }

    const [result] = await db.query(
      'DELETE FROM categories WHERE category_id = ?',
      [id]
    )

    if (result.affectedRows === 1) {
      res.send({ message: '栏目删除成功' })
    } else {
      res.send({ message: `无法删除ID为${id}的栏目，可能栏目不存在` })
    }
  } catch (err) {
    res.status(500).send({ message: `删除栏目时出错: ${err.message}` })
  }
}
