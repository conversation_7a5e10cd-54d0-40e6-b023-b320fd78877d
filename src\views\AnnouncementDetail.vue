<template>
  <div class="announcement-detail">
    <div class="back-button">
      <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回列表</el-button>
    </div>
    <h1>{{ announcement.title }}</h1>
    <div class="meta">
      <span>{{ formatDate(announcement.created_at) }}</span>
    </div>
    <div class="content">{{ announcement.content }}</div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      announcement: {}
    }
  },
  created () {
    this.fetchAnnouncementDetail()
  },
  methods: {
    async fetchAnnouncementDetail () {
      try {
        console.log('正在获取公告详情:', this.$route.params.id)
        const res = await this.$http.get(`/announcements/${this.$route.params.id}`)
        console.log('公告详情响应:', res.data)

        if (res.data) {
          this.announcement = res.data
        } else {
          console.error('公告详情数据格式不正确:', res.data)
          // 使用模拟数据作为备份
          this.announcement = {
            announcement_id: this.$route.params.id,
            title: '公告标题',
            content: '公告内容...',
            created_at: new Date().toLocaleString()
          }
        }
      } catch (err) {
        console.error('获取公告详情失败:', err)
        // 使用模拟数据作为备份
        this.announcement = {
          announcement_id: this.$route.params.id,
          title: '公告标题',
          content: '公告内容...',
          created_at: new Date().toLocaleString()
        }
      }
    },
    formatDate (dateString) {
      return new Date(dateString).toLocaleString()
    },
    goBack () {
      // 返回上一页
      this.$router.push('/announcements')
    }
  }
}
</script>

<style scoped>
.announcement-detail {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.back-button {
  margin-bottom: 20px;
}

h1 {
  margin-bottom: 10px;
}

.meta {
  color: #999;
  margin-bottom: 20px;
}

.content {
  line-height: 1.8;
  white-space: pre-line;
}
</style>
