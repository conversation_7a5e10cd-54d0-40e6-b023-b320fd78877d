/**
 * API接口封装
 */
import * as mock from './mock'

// 是否使用模拟数据
const USE_MOCK = false

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @returns {Promise} 登录结果
 */
export function userLogin (data) {
  if (USE_MOCK) {
    return Promise.resolve(mock.mockUserLogin(data.username, data.password))
  }
  // 实际API调用由axios实例处理
  return Promise.resolve()
}

/**
 * 管理员登录
 * @param {Object} data 登录数据
 * @returns {Promise} 登录结果
 */
export function adminLogin (data) {
  if (USE_MOCK) {
    return Promise.resolve(mock.mockAdminLogin(data.username, data.password))
  }
  // 实际API调用由axios实例处理
  return Promise.resolve()
}

/**
 * 获取新闻列表
 * @param {Object} params 查询参数
 * @returns {Promise} 新闻列表
 */
export function getNewsList (params) {
  if (USE_MOCK) {
    return Promise.resolve(mock.getNewsList(params?.categoryId))
  }
  // 实际API调用由axios实例处理
  return Promise.resolve()
}

/**
 * 获取新闻详情
 * @param {number} id 新闻ID
 * @returns {Promise} 新闻详情
 */
export function getNewsDetail (id) {
  if (USE_MOCK) {
    return Promise.resolve(mock.getNewsDetail(id))
  }
  // 实际API调用由axios实例处理
  return Promise.resolve()
}

/**
 * 获取分类列表
 * @returns {Promise} 分类列表
 */
export function getCategoryList () {
  if (USE_MOCK) {
    return Promise.resolve(mock.getCategoryList())
  }
  // 实际API调用由axios实例处理
  return Promise.resolve()
}

/**
 * 获取公告列表
 * @returns {Promise} 公告列表
 */
export function getAnnouncementList () {
  if (USE_MOCK) {
    return Promise.resolve(mock.getAnnouncementList())
  }
  // 实际API调用由axios实例处理
  return Promise.resolve()
}

export default {
  userLogin,
  adminLogin,
  getNewsList,
  getNewsDetail,
  getCategoryList,
  getAnnouncementList
}
