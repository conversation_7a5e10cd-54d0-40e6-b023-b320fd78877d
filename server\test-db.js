/**
 * 数据库连接测试脚本
 */
const db = require('./db');

async function testDatabaseConnection() {
  try {
    console.log('正在测试数据库连接...');
    
    // 测试用户表
    console.log('测试用户表...');
    const [users] = await db.query('SELECT * FROM users');
    console.log(`用户表中有 ${users.length} 条记录`);
    
    // 测试管理员表
    console.log('测试管理员表...');
    const [admins] = await db.query('SELECT * FROM admin_users');
    console.log(`管理员表中有 ${admins.length} 条记录`);
    
    // 测试栏目表
    console.log('测试栏目表...');
    const [categories] = await db.query('SELECT * FROM categories');
    console.log(`栏目表中有 ${categories.length} 条记录`);
    
    // 测试新闻表
    console.log('测试新闻表...');
    const [news] = await db.query('SELECT * FROM news');
    console.log(`新闻表中有 ${news.length} 条记录`);
    
    // 测试公告表
    console.log('测试公告表...');
    const [announcements] = await db.query('SELECT * FROM announcements');
    console.log(`公告表中有 ${announcements.length} 条记录`);
    
    console.log('数据库连接测试成功！');
  } catch (err) {
    console.error('数据库连接测试失败:', err);
  } finally {
    process.exit();
  }
}

// 执行测试
testDatabaseConnection();
