<template>
  <div class="news-detail">
    <div class="back-button">
      <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回列表</el-button>
    </div>
    <h1>{{ news.title }}</h1>
    <div class="meta">
      <span>{{ news.created_at }}</span>
      <span>栏目: {{ news.category_name }}</span>
    </div>
    <div class="content" v-html="news.content"></div>

    <div class="comments">
      <h3>评论</h3>
      <div v-if="comments.length === 0" class="no-comments">
        暂无评论
      </div>
      <div v-else>
        <div v-for="comment in comments" :key="comment.comment_id" class="comment">
          <p>{{ comment.comment_content }}</p>
          <span class="comment-meta">
            {{ comment.created_at }} | 用户发布
          </span>
        </div>
      </div>

      <div class="add-comment" v-if="isLoggedIn">
        <textarea v-model="newComment" placeholder="请输入评论内容"></textarea>
        <button @click="submitComment">提交评论</button>
      </div>
      <div v-else class="login-tip">
        请<a href="/login">登录</a>后发表评论
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      news: {},
      comments: [],
      newComment: '',
      isLoggedIn: false
    }
  },
  created () {
    this.fetchNewsDetail()
    this.fetchComments()
    this.checkLoginStatus()
  },
  methods: {
    async fetchNewsDetail () {
      try {
        console.log('正在获取新闻详情:', this.$route.params.id)
        // 修正API路径，移除多余的/api前缀
        const res = await this.$http.get(`/news/${this.$route.params.id}`)
        console.log('新闻详情响应:', res.data)

        if (res.data) {
          this.news = res.data
        } else {
          console.error('新闻详情数据格式不正确:', res.data)
          // 使用模拟数据作为备份
          this.news = {
            news_id: this.$route.params.id,
            title: '新闻标题',
            content: '新闻内容...',
            category_id: 1,
            category_name: '默认栏目',
            created_at: new Date().toLocaleString()
          }
        }
      } catch (err) {
        console.error('获取新闻详情失败:', err)
        // 使用模拟数据作为备份
        this.news = {
          news_id: this.$route.params.id,
          title: '新闻标题',
          content: '新闻内容...',
          category_id: 1,
          category_name: '默认栏目',
          created_at: new Date().toLocaleString()
        }
      }
    },
    async fetchComments () {
      try {
        console.log('正在获取评论...')
        // 使用正确的API路径
        const newsId = this.$route.params.id
        console.log(`获取新闻ID: ${newsId} 的评论`)
        const res = await this.$http.get(`/news/${newsId}/comments`)
        console.log('评论响应:', res.data)

        if (res.data && Array.isArray(res.data)) {
          this.comments = res.data
        } else {
          console.error('评论数据格式不正确:', res.data)
          // 使用模拟数据作为备份
          this.comments = [
            {
              comment_id: 1,
              news_id: newsId,
              user_id: 1,
              username: '用户',
              comment_content: '这是一条测试评论',
              created_at: new Date().toLocaleString()
            },
            {
              comment_id: 2,
              news_id: newsId,
              user_id: 2,
              username: '用户',
              comment_content: '这篇新闻很有意思',
              created_at: new Date().toLocaleString()
            }
          ]
        }
      } catch (err) {
        console.error('获取评论失败:', err)
        // 使用模拟数据作为备份
        this.comments = [
          {
            comment_id: 1,
            news_id: this.$route.params.id,
            user_id: 1,
            username: '用户',
            comment_content: '这是一条测试评论',
            created_at: new Date().toLocaleString()
          },
          {
            comment_id: 2,
            news_id: this.$route.params.id,
            user_id: 2,
            username: '用户',
            comment_content: '这篇新闻很有意思',
            created_at: new Date().toLocaleString()
          }
        ]
      }
    },
    checkLoginStatus () {
      // 检查用户登录状态
      this.isLoggedIn = localStorage.getItem('userToken') !== null || localStorage.getItem('adminToken') !== null
    },
    goBack () {
      // 返回上一页
      this.$router.push('/news')
    },
    async submitComment () {
      if (!this.newComment.trim()) {
        this.$message.warning('评论内容不能为空')
        return
      }

      try {
        console.log('正在提交评论...')
        // 使用正确的API路径
        const newsId = this.$route.params.id
        console.log(`提交评论到新闻ID: ${newsId}`)
        await this.$http.post(`/news/${newsId}/comments`, {
          comment_content: this.newComment
        })
        this.$message.success('评论提交成功')
        this.newComment = ''
        this.fetchComments()
      } catch (err) {
        console.error('提交评论失败:', err)
        this.$message.error('评论提交失败，请稍后再试')
      }
    }
  }
}
</script>

<style scoped>
.news-detail {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  padding-top: 10px;
}
.back-button {
  margin-bottom: 20px;
}
.meta {
  color: #666;
  margin: 10px 0;
}
.meta span {
  margin-right: 15px;
}
.content {
  margin: 20px 0;
  line-height: 1.6;
}
.comments {
  margin-top: 40px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}
.comment {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eee;
}
.comment-meta {
  color: #999;
  font-size: 12px;
}
.add-comment textarea {
  width: 100%;
  height: 100px;
  margin: 10px 0;
}
.login-tip {
  margin: 20px 0;
  color: #666;
}
</style>
