import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import * as api from './api'
import axios from 'axios'

// 注册ElementUI组件库和样式
Vue.use(ElementUI)
Vue.prototype.$api = api
// 创建一个axios实例，连接到后端API
Vue.prototype.$http = axios.create({
  baseURL: 'http://localhost:3003/api', // 更新为3003端口
  timeout: 5000
})

// 添加请求拦截器
Vue.prototype.$http.interceptors.request.use(
  config => {
    // 在请求头中添加token
    const token = localStorage.getItem('adminToken') || localStorage.getItem('userToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      // 同时添加x-access-token头，因为后端可能使用这个头
      config.headers['x-access-token'] = token
    }
    console.log('请求配置:', {
      url: config.url,
      method: config.method,
      headers: {
        Authorization: config.headers.Authorization,
        'x-access-token': config.headers['x-access-token']
      }
    })
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
Vue.prototype.$http.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // 处理错误响应
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('API错误:', error.response.status, error.response.data)
      ElementUI.Message.error(error.response.data?.message || '请求失败')
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('网络错误:', error.request)
      ElementUI.Message.error('网络错误，请检查服务器是否运行')
    } else {
      // 请求配置出错
      console.error('请求错误:', error.message)
      ElementUI.Message.error('请求配置错误')
    }
    return Promise.reject(error)
  }
)

Vue.config.productionTip = false

// 初始化Vuex状态
store.dispatch('initializeStore')

// 创建Vue实例
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
