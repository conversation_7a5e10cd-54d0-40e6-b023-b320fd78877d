const express = require('express')
const cors = require('cors')
const bodyParser = require('body-parser')
const db = require('./db')
const app = express()

// 中间件
app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: false }))
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8081'], // 允许多个来源
  credentials: true, // 允许携带凭证
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // 允许的HTTP方法
  allowedHeaders: ['Content-Type', 'Authorization', 'x-access-token'] // 允许的请求头
}))

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`)
  next()
})

// 路由
// 使用直接MySQL连接的认证路由
const directAuthRoutes = require('./direct-auth-routes')
// 其他路由
const newsRoutes = require('./routes/news.routes')
const categoryRoutes = require('./routes/category.routes')
const announcementRoutes = require('./routes/announcement.routes')
const commentRoutes = require('./routes/comment.routes')

// 注册路由
app.use('/api', directAuthRoutes) // 使用直接MySQL连接的认证路由
app.use('/api', newsRoutes) // 新闻路由
app.use('/api', categoryRoutes) // 栏目路由
app.use('/api', announcementRoutes) // 公告路由
app.use('/api', commentRoutes) // 评论路由

// 添加错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err.stack)
  res.status(500).json({
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  })
})

// 测试数据库连接
async function testDbConnection() {
  try {
    const connection = await db.getConnection();
    console.log('数据库连接成功');
    connection.release();
  } catch (err) {
    console.error('数据库连接失败:', err);
  }
}

// 执行数据库连接测试
testDbConnection();

const PORT = process.env.PORT || 3003 // 使用3003端口
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`)
})
